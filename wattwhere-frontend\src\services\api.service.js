import { auth } from '../config/firebase';

const API_URL = process.env.NODE_ENV === 'production'
  ? 'https://api-hckcwa25ra-uc.a.run.app/api'
  : 'http://localhost:5000/api';

/**
 * Get the current user's ID token
 * @returns {Promise<string>} The ID token
 */
const getToken = async () => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('User not authenticated');
  }
  return await user.getIdToken();
};

/**
 * Make an API request with authentication
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} Response data
 */
const apiRequest = async (endpoint, options = {}) => {
  try {
    // Get authentication token
    const token = await getToken();

    // Set default headers
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers
    };

    // Make the request
    const response = await fetch(`${API_URL}${endpoint}`, {
      ...options,
      headers
    });

    // Parse the response
    const data = await response.json();

    // Check for errors
    if (!response.ok) {
      throw new Error(data.message || 'Something went wrong');
    }

    return data;
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
};

/**
 * Authentication API
 */
const authAPI = {
  verifyToken: async (token) => {
    const response = await fetch(`${API_URL}/auth/verify-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ token })
    });

    return await response.json();
  }
};

/**
 * Suggestions API
 */
const suggestionsAPI = {
  createSuggestion: async (formData) => {
    try {
      const token = await getToken();

      console.log('Sending suggestion request to:', `${API_URL}/suggestions`);
      console.log('FormData contents:');
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      const response = await fetch(`${API_URL}/suggestions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData // FormData for file upload
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log('Success response:', result);
      return result;
    } catch (error) {
      console.error('createSuggestion error:', error);
      throw error;
    }
  },

  getUserSuggestions: async () => {
    return await apiRequest('/suggestions/user/me');
  },

  getSuggestion: async (id) => {
    return await apiRequest(`/suggestions/${id}`);
  }
};

/**
 * User API
 */
const userAPI = {
  getCurrentUser: async () => {
    return await apiRequest('/users/me');
  },

  updateProfile: async (userData) => {
    return await apiRequest('/users/me', {
      method: 'PATCH',
      body: JSON.stringify(userData)
    });
  }
};

/**
 * Admin API
 */
const adminAPI = {
  getAllSuggestions: async (filters = {}) => {
    const queryParams = new URLSearchParams(filters).toString();
    return await apiRequest(`/admin/suggestions?${queryParams}`);
  },

  updateSuggestionStatus: async (id, status) => {
    return await apiRequest(`/admin/suggestions/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status })
    });
  },

  getAnalytics: async () => {
    return await apiRequest('/admin/analytics');
  },

  getDemandAnalytics: async (options = {}) => {
    const queryParams = new URLSearchParams(options).toString();
    return await apiRequest(`/admin/demand-analytics?${queryParams}`);
  }
};

export {
  authAPI,
  suggestionsAPI,
  userAPI,
  adminAPI
};
