const { db } = require('../config/firebase');
const { ApiError } = require('../middleware/error.middleware');
const { uploadFileToStorage } = require('../utils/storage.utils');

/**
 * Create a new charging spot suggestion
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const createSuggestion = async (req, res, next) => {
  try {
    console.log('Create suggestion request received');
    console.log('Request body:', req.body);
    console.log('Request file:', req.file ? {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    } : 'No file');

    const {
      address,
      coordinates,
      description,
      hasEV,
      weeklyUse
    } = req.body;

    // Validate required fields
    if (!address || !coordinates) {
      console.error('Validation error: Missing address or coordinates');
      throw new ApiError(400, 'Address and coordinates are required');
    }

    // Get user from request (set by auth middleware)
    const { uid, email, name } = req.user;
    console.log('User info:', { uid, email, name });

    // Upload photo if provided
    let photoUrl = null;
    if (req.file) {
      console.log('Uploading photo to storage...');
      photoUrl = await uploadFileToStorage(req.file, uid);
      console.log('Photo uploaded successfully:', photoUrl);
    } else {
      console.log('No photo to upload');
    }

    // Parse coordinates safely
    let parsedCoordinates;
    try {
      parsedCoordinates = typeof coordinates === 'string' ? JSON.parse(coordinates) : coordinates;
    } catch (parseError) {
      console.error('Coordinates parsing error:', parseError);
      throw new ApiError(400, 'Invalid coordinates format');
    }

    // Create suggestion document
    const suggestionData = {
      id: `spot-${Date.now()}`,
      userId: uid,
      username: name || email.split('@')[0],
      timestamp: new Date().toISOString(),
      location: {
        address,
        coordinates: parsedCoordinates
      },
      description: description || '',
      photo: photoUrl,
      hasEV: hasEV === 'true' || hasEV === true,
      weeklyUse: weeklyUse || '',
      status: 'pending'
    };

    console.log('Saving suggestion to Firestore:', suggestionData);

    // Save to Firestore
    const docRef = await db.collection('suggestions').add(suggestionData);

    // Update with the document ID
    await docRef.update({ id: docRef.id });
    suggestionData.id = docRef.id;

    console.log('Suggestion saved successfully with ID:', docRef.id);

    res.status(201).json({
      status: 'success',
      data: suggestionData
    });
  } catch (error) {
    console.error('Create suggestion error:', error);
    console.error('Error stack:', error.stack);
    next(new ApiError(error.statusCode || 500, error.message || 'Internal server error'));
  }
};

/**
 * Get a specific suggestion by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const getSuggestion = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get suggestion from Firestore
    const suggestionDoc = await db.collection('suggestions').doc(id).get();

    if (!suggestionDoc.exists) {
      throw new ApiError(404, 'Suggestion not found');
    }

    const suggestion = suggestionDoc.data();

    res.status(200).json({
      status: 'success',
      data: suggestion
    });
  } catch (error) {
    console.error('Get suggestion error:', error);
    next(new ApiError(error.statusCode || 400, error.message));
  }
};

/**
 * Get all suggestions for the current user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const getUserSuggestions = async (req, res, next) => {
  try {
    const { uid } = req.user;

    let suggestions = [];

    try {
      // Try to query with ordering (requires index)
      const suggestionsSnapshot = await db.collection('suggestions')
        .where('userId', '==', uid)
        .orderBy('timestamp', 'desc')
        .get();

      suggestionsSnapshot.forEach(doc => {
        suggestions.push(doc.data());
      });
    } catch (indexError) {
      console.warn('Index error, falling back to unordered query:', indexError.message);

      // Fallback: query without ordering if index doesn't exist
      const suggestionsSnapshot = await db.collection('suggestions')
        .where('userId', '==', uid)
        .get();

      // Get all documents and sort them in memory
      suggestionsSnapshot.forEach(doc => {
        suggestions.push(doc.data());
      });

      // Sort in memory by timestamp (descending)
      suggestions.sort((a, b) => {
        const timestampA = new Date(a.timestamp).getTime();
        const timestampB = new Date(b.timestamp).getTime();
        return timestampB - timestampA; // Descending order
      });

      // Log a message about the missing index
      console.warn(
        'Missing Firestore index for suggestions collection. ' +
        'Please create an index for userId (ascending) and timestamp (descending). ' +
        'Visit: https://console.firebase.google.com/project/wattwhere-f8f65/firestore/indexes'
      );
    }

    res.status(200).json({
      status: 'success',
      results: suggestions.length,
      data: suggestions
    });
  } catch (error) {
    console.error('Get user suggestions error:', error);
    next(new ApiError(400, error.message));
  }
};

module.exports = {
  createSuggestion,
  getSuggestion,
  getUserSuggestions
};
