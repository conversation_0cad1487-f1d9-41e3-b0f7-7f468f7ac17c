const multer = require('multer');
const path = require('path');
const { ApiError } = require('./error.middleware');

// Configure storage
const storage = multer.memoryStorage();

// File filter to only allow images
const fileFilter = (req, file, cb) => {
  // Accept only image files
  if (file && file.mimetype && file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else if (!file) {
    cb(null, false); // No file is okay
  } else {
    cb(new ApiError(400, 'Only image files are allowed'), false);
  }
};

// Configure multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
    fieldSize: 10 * 1024 * 1024, // 10MB for field data
    fields: 10, // Max number of non-file fields
    parts: 20, // Max number of parts
  },
});

// Wrapper to handle multer errors
const uploadImage = (req, res, next) => {
  const uploadSingle = upload.single('photo');

  uploadSingle(req, res, (err) => {
    if (err) {
      console.error('Multer error:', err);

      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return next(new ApiError(400, 'File too large. Maximum size is 5MB.'));
        } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          return next(new ApiError(400, 'Unexpected file field.'));
        } else {
          return next(new ApiError(400, `Upload error: ${err.message}`));
        }
      } else {
        return next(new ApiError(400, err.message || 'File upload failed'));
      }
    }

    next();
  });
};

module.exports = {
  uploadImage
};
