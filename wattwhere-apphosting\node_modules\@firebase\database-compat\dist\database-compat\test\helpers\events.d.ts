/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * A set of functions to clean up event handlers.
 */
export declare let eventCleanupHandlers: any[];
/** Clean up outstanding event handlers */
export declare function eventCleanup(): void;
/**
 * Creates a struct which waits for many events.
 * @param pathAndEvents - an array of tuples of [Firebase, [event type strings]]
 */
export declare function eventTestHelper(pathAndEvents: any, helperName?: any): {
    promise: Promise<unknown>;
    initPromise: Promise<unknown>;
    waiter: () => boolean;
    watchesInitializedWaiter: () => boolean;
    unregister: () => void;
    addExpectedEvents(moreEvents: any): void;
};
