{"ast": null, "code": "import React,{useState,useCallback,useEffect}from'react';import{Box,Container,Typography,Button,Paper,TextField,Stack,Avatar,IconButton,CircularProgress,useTheme,useMediaQuery,InputAdornment,Tooltip,Alert,FormControl,FormControlLabel,RadioGroup,Radio,Select,MenuItem,InputLabel,Divider,Snackbar}from'@mui/material';import{LocationOn,MyLocation,ArrowBack,CloudUpload,Delete,ZoomIn,ZoomOut}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import{GoogleMap,useJsApiLoader,Marker}from'@react-google-maps/api';import{modernMapStyle,markerIcons}from'../utils/mapStyles';import{useAuth}from'../contexts/AuthContext';import{suggestionsAPI}from'../services/api.service';// Google Maps container style\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const mapContainerStyle={width:'100%',height:'350px',borderRadius:'12px'};// Default center (will be overridden by user's location)\nconst defaultCenter={lat:40.7128,// New York\nlng:-74.0060};const libraries=[\"places\"];const SuggestionForm=()=>{const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('sm'));const navigate=useNavigate();const{currentUser}=useAuth();const[address,setAddress]=useState('');const[description,setDescription]=useState('');const[photo,setPhoto]=useState(null);const[photoPreview,setPhotoPreview]=useState('');const[isLoading,setIsLoading]=useState(false);const[isGettingLocation,setIsGettingLocation]=useState(false);const[mapCenter,setMapCenter]=useState(defaultCenter);const[markerPosition,setMarkerPosition]=useState(null);const[mapZoom,setMapZoom]=useState(15);const[mapError,setMapError]=useState('');const[hasEV,setHasEV]=useState('');const[weeklyUse,setWeeklyUse]=useState('');const[error,setError]=useState('');const[showError,setShowError]=useState(false);// Load Google Maps API\nconst{isLoaded,loadError}=useJsApiLoader({googleMapsApiKey:process.env.REACT_APP_GOOGLE_MAPS_API_KEY,libraries});const handleBack=()=>{navigate('/');};// Handle map click to set marker\nconst handleMapClick=useCallback(event=>{const lat=event.latLng.lat();const lng=event.latLng.lng();setMarkerPosition({lat,lng});setAddress(\"Lat: \".concat(lat.toFixed(6),\", Lng: \").concat(lng.toFixed(6)));// Optional: Use Google's Geocoding API to get the address from coordinates\n// This would require additional API calls\n},[]);// Zoom controls\nconst handleZoomIn=()=>{setMapZoom(prev=>Math.min(prev+1,20));};const handleZoomOut=()=>{setMapZoom(prev=>Math.max(prev-1,1));};const handleGetCurrentLocation=()=>{setIsGettingLocation(true);setMapError('');if(navigator.geolocation){navigator.geolocation.getCurrentPosition(position=>{const lat=position.coords.latitude;const lng=position.coords.longitude;// Update map center and marker\nsetMapCenter({lat,lng});setMarkerPosition({lat,lng});// For demo purposes, just show the coordinates\nsetAddress(\"Lat: \".concat(lat.toFixed(6),\", Lng: \").concat(lng.toFixed(6)));setIsGettingLocation(false);},error=>{console.error('Error getting location:',error);setIsGettingLocation(false);setMapError('Unable to retrieve your location. Please enter the address manually or click on the map.');});}else{setIsGettingLocation(false);setMapError('Geolocation is not supported by your browser. Please enter the address manually or click on the map.');}};const handlePhotoChange=e=>{if(e.target.files&&e.target.files[0]){const selectedFile=e.target.files[0];setPhoto(selectedFile);// Create a preview\nconst reader=new FileReader();reader.onload=event=>{setPhotoPreview(event.target.result);};reader.readAsDataURL(selectedFile);}};const handleRemovePhoto=()=>{setPhoto(null);setPhotoPreview('');};const handleSubmit=async e=>{e.preventDefault();if(!address.trim()||!markerPosition){setMapError('Please select a location on the map or use the current location button.');return;}if(!hasEV){setError('Please indicate whether you own an electric vehicle.');setShowError(true);return;}if(!weeklyUse){setError('Please indicate how many times you would use this charging point weekly.');setShowError(true);return;}setIsLoading(true);setError('');try{// Create form data for submission\nconst formData=new FormData();formData.append('address',address.trim());formData.append('coordinates',JSON.stringify(markerPosition));formData.append('description',description.trim());formData.append('hasEV',hasEV==='yes'?'true':'false');formData.append('weeklyUse',weeklyUse.toString());// Add photo if available\nif(photo){formData.append('photo',photo);}// Send data to backend\nconst response=await suggestionsAPI.createSuggestion(formData);if(response.status==='success'){// Navigate to confirmation page\nnavigate('/confirmation');}else{throw new Error(response.message||'Failed to submit suggestion');}}catch(error){console.error('Submission error:',error);setError(error.message||'An error occurred while submitting your suggestion. Please try again.');setShowError(true);}finally{setIsLoading(false);}};// Handle error snackbar close\nconst handleErrorClose=()=>{setShowError(false);};return/*#__PURE__*/_jsxs(Box,{sx:{minHeight:'100vh',bgcolor:'#F5F5F5',pt:{xs:8,md:12},pb:6},children:[/*#__PURE__*/_jsx(Snackbar,{open:showError,autoHideDuration:6000,onClose:handleErrorClose,message:error,anchorOrigin:{vertical:'top',horizontal:'center'}}),/*#__PURE__*/_jsxs(Container,{maxWidth:\"md\",children:[/*#__PURE__*/_jsx(IconButton,{onClick:handleBack,sx:{mb:2,color:'#222222','&:hover':{bgcolor:'rgba(46, 196, 182, 0.08)'}},children:/*#__PURE__*/_jsx(ArrowBack,{})}),/*#__PURE__*/_jsxs(Paper,{elevation:0,sx:{p:{xs:3,md:5},borderRadius:4,bgcolor:'white',boxShadow:'0 8px 32px rgba(0,0,0,0.08)',position:'relative',overflow:'hidden'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',width:'200px',height:'200px',borderRadius:'50%',background:'radial-gradient(circle, rgba(46,196,182,0.08) 0%, rgba(46,196,182,0) 70%)',top:'-100px',right:'-100px',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',width:'200px',height:'200px',borderRadius:'50%',background:'radial-gradient(circle, rgba(255,90,95,0.08) 0%, rgba(255,90,95,0) 70%)',bottom:'-100px',left:'-100px',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Stack,{direction:\"row\",alignItems:\"center\",spacing:1,sx:{mb:4},children:[/*#__PURE__*/_jsx(LocationOn,{sx:{color:'#FF5A5F',fontSize:28}}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",sx:{fontWeight:800,color:'#222222'},children:\"Suggest a Charging Spot\"})]}),/*#__PURE__*/_jsx(\"form\",{onSubmit:handleSubmit,children:/*#__PURE__*/_jsxs(Stack,{spacing:3,children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:600,mb:1,color:'#222222'},children:\"Location\"}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:\"Enter address or use current location\",value:address,onChange:e=>setAddress(e.target.value),variant:\"outlined\",required:true,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(LocationOn,{sx:{color:'#2EC4B6'}})}),endAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(Tooltip,{title:\"Use current location\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleGetCurrentLocation,disabled:isGettingLocation,sx:{color:'#FF5A5F'},children:isGettingLocation?/*#__PURE__*/_jsx(CircularProgress,{size:24,color:\"inherit\"}):/*#__PURE__*/_jsx(MyLocation,{})})})}),sx:{borderRadius:2,bgcolor:'#F5F5F5','&:hover':{bgcolor:'#F0F0F0'}}}}),mapError&&/*#__PURE__*/_jsx(Alert,{severity:\"warning\",sx:{mt:2,borderRadius:2},children:mapError}),/*#__PURE__*/_jsx(Box,{sx:{mt:2,position:'relative'},children:isLoaded?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Paper,{elevation:0,sx:{borderRadius:3,overflow:'hidden',boxShadow:'0 4px 20px rgba(0,0,0,0.08)',border:'1px solid rgba(0,0,0,0.05)'},children:/*#__PURE__*/_jsx(GoogleMap,{mapContainerStyle:mapContainerStyle,center:mapCenter,zoom:mapZoom,onClick:handleMapClick,options:{fullscreenControl:false,streetViewControl:false,mapTypeControl:false,zoomControl:false,styles:modernMapStyle,disableDefaultUI:true,gestureHandling:'greedy'},children:markerPosition&&/*#__PURE__*/_jsx(Marker,{position:markerPosition,animation:2// DROP animation\n,icon:markerIcons.selected})})}),/*#__PURE__*/_jsxs(Box,{sx:{position:'absolute',right:16,top:16,display:'flex',flexDirection:'column',bgcolor:'white',borderRadius:2,boxShadow:'0 4px 12px rgba(0,0,0,0.15)',p:0.5,'& .MuiIconButton-root':{color:'#222222',transition:'all 0.2s ease','&:hover':{bgcolor:'rgba(46, 196, 182, 0.1)',color:'#2EC4B6'}}},children:[/*#__PURE__*/_jsx(IconButton,{onClick:handleZoomIn,size:\"small\",children:/*#__PURE__*/_jsx(ZoomIn,{})}),/*#__PURE__*/_jsx(Divider,{sx:{my:0.5}}),/*#__PURE__*/_jsx(IconButton,{onClick:handleZoomOut,size:\"small\",children:/*#__PURE__*/_jsx(ZoomOut,{})})]})]}):/*#__PURE__*/_jsx(Paper,{elevation:0,sx:{height:350,bgcolor:'#F5F5F5',borderRadius:3,display:'flex',alignItems:'center',justifyContent:'center',boxShadow:'0 4px 20px rgba(0,0,0,0.08)',border:'1px solid rgba(0,0,0,0.05)'},children:loadError?/*#__PURE__*/_jsx(Typography,{color:\"error\",children:\"Error loading maps. Please try again later.\"}):/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsx(CircularProgress,{color:\"primary\",size:40,sx:{mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Loading map...\"})]})})}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:'block',mt:1,color:'text.secondary',textAlign:'center'},children:\"Click on the map to set a location or use the \\\"Current Location\\\" button\"})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:600,mb:1,color:'#222222'},children:\"Description (Optional)\"}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:\"Why is this a good location for a charging station?\",value:description,onChange:e=>setDescription(e.target.value),variant:\"outlined\",multiline:true,rows:3,InputProps:{sx:{borderRadius:2,bgcolor:'#F5F5F5','&:hover':{bgcolor:'#F0F0F0'}}}})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:600,mb:1,color:'#222222'},children:\"Do you own an electric vehicle?\"}),/*#__PURE__*/_jsx(FormControl,{required:true,sx:{mb:3},children:/*#__PURE__*/_jsxs(RadioGroup,{row:true,value:hasEV,onChange:e=>setHasEV(e.target.value),children:[/*#__PURE__*/_jsx(FormControlLabel,{value:\"yes\",control:/*#__PURE__*/_jsx(Radio,{sx:{color:'#2EC4B6','&.Mui-checked':{color:'#2EC4B6'}}}),label:\"Yes\"}),/*#__PURE__*/_jsx(FormControlLabel,{value:\"no\",control:/*#__PURE__*/_jsx(Radio,{sx:{color:'#2EC4B6','&.Mui-checked':{color:'#2EC4B6'}}}),label:\"No\"})]})})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:600,mb:1,color:'#222222'},children:\"If there was a charging point here, how many times would you use it weekly?\"}),/*#__PURE__*/_jsx(FormControl,{fullWidth:true,required:true,sx:{mb:3},children:/*#__PURE__*/_jsxs(Select,{value:weeklyUse,onChange:e=>setWeeklyUse(e.target.value),displayEmpty:true,sx:{borderRadius:2,bgcolor:'#F5F5F5','&:hover':{bgcolor:'#F0F0F0'}},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"\",disabled:true,children:\"Select frequency\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"1\",children:\"1 time per week\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"2\",children:\"2 times per week\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"3\",children:\"3 times per week\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"4\",children:\"4 times per week\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"5\",children:\"5 or more times per week\"})]})})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:600,mb:1,color:'#222222'},children:\"Photo (Optional)\"}),!photoPreview?/*#__PURE__*/_jsxs(Button,{component:\"label\",variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(CloudUpload,{}),sx:{py:1.5,px:2,borderColor:'rgba(0,0,0,0.12)',borderStyle:'dashed',color:'#666666',borderRadius:2,'&:hover':{borderColor:'#2EC4B6',bgcolor:'rgba(46, 196, 182, 0.04)'}},children:[\"Upload Photo\",/*#__PURE__*/_jsx(\"input\",{type:\"file\",hidden:true,accept:\"image/*\",onChange:handlePhotoChange})]}):/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',width:'fit-content'},children:[/*#__PURE__*/_jsx(Box,{component:\"img\",src:photoPreview,alt:\"Location preview\",sx:{width:'100%',maxWidth:300,height:'auto',borderRadius:2,boxShadow:'0 4px 12px rgba(0,0,0,0.1)'}}),/*#__PURE__*/_jsx(IconButton,{onClick:handleRemovePhoto,sx:{position:'absolute',top:8,right:8,bgcolor:'rgba(255,255,255,0.8)',color:'#FF5A5F','&:hover':{bgcolor:'rgba(255,255,255,0.9)'}},children:/*#__PURE__*/_jsx(Delete,{})})]})]}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",size:\"large\",disabled:isLoading||!address.trim(),sx:{bgcolor:'#2EC4B6',color:'white',py:1.5,fontWeight:700,fontSize:'1.1rem',borderRadius:99,mt:2,'&:hover':{bgcolor:'#23C3B1',transform:'translateY(-2px)',boxShadow:'0 6px 20px rgba(46, 196, 182, 0.3)'},transition:'all 0.3s ease'},children:isLoading?/*#__PURE__*/_jsx(CircularProgress,{size:24,color:\"inherit\"}):'Submit Suggestion'})]})})]})]})]})]});};export default SuggestionForm;", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useEffect", "Box", "Container", "Typography", "<PERSON><PERSON>", "Paper", "TextField", "<PERSON><PERSON>", "Avatar", "IconButton", "CircularProgress", "useTheme", "useMediaQuery", "InputAdornment", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "FormControl", "FormControlLabel", "RadioGroup", "Radio", "Select", "MenuItem", "InputLabel", "Divider", "Snackbar", "LocationOn", "MyLocation", "ArrowBack", "CloudUpload", "Delete", "ZoomIn", "ZoomOut", "useNavigate", "GoogleMap", "useJsApiLoader", "<PERSON><PERSON>", "modernMapStyle", "markerIcons", "useAuth", "suggestionsAPI", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "mapContainerStyle", "width", "height", "borderRadius", "defaultCenter", "lat", "lng", "libraries", "SuggestionForm", "theme", "isMobile", "breakpoints", "down", "navigate", "currentUser", "address", "<PERSON><PERSON><PERSON><PERSON>", "description", "setDescription", "photo", "setPhot<PERSON>", "photoPreview", "setPhotoPreview", "isLoading", "setIsLoading", "isGettingLocation", "setIsGettingLocation", "mapCenter", "setMapCenter", "markerPosition", "setMarkerPosition", "mapZoom", "setMapZoom", "mapError", "setMapError", "hasEV", "setHasEV", "weeklyUse", "setWeeklyUse", "error", "setError", "showError", "setShowError", "isLoaded", "loadError", "googleMapsApiKey", "process", "env", "REACT_APP_GOOGLE_MAPS_API_KEY", "handleBack", "handleMapClick", "event", "latLng", "concat", "toFixed", "handleZoomIn", "prev", "Math", "min", "handleZoomOut", "max", "handleGetCurrentLocation", "navigator", "geolocation", "getCurrentPosition", "position", "coords", "latitude", "longitude", "console", "handlePhotoChange", "e", "target", "files", "selectedFile", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleRemovePhoto", "handleSubmit", "preventDefault", "trim", "formData", "FormData", "append", "JSON", "stringify", "toString", "response", "createSuggestion", "status", "Error", "message", "handleErrorClose", "sx", "minHeight", "bgcolor", "pt", "xs", "md", "pb", "children", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "max<PERSON><PERSON><PERSON>", "onClick", "mb", "color", "elevation", "p", "boxShadow", "overflow", "background", "top", "right", "zIndex", "bottom", "left", "direction", "alignItems", "spacing", "fontSize", "variant", "fontWeight", "onSubmit", "fullWidth", "placeholder", "value", "onChange", "required", "InputProps", "startAdornment", "endAdornment", "title", "disabled", "size", "severity", "mt", "border", "center", "zoom", "options", "fullscreenControl", "streetViewControl", "mapTypeControl", "zoomControl", "styles", "disableDefaultUI", "<PERSON><PERSON><PERSON><PERSON>", "animation", "icon", "selected", "display", "flexDirection", "transition", "my", "justifyContent", "textAlign", "multiline", "rows", "row", "control", "label", "displayEmpty", "component", "startIcon", "py", "px", "borderColor", "borderStyle", "type", "hidden", "accept", "src", "alt", "transform"], "sources": ["D:/WattWhere_Project/wattwhere-fro/wattwhere-frontend/src/components/SuggestionForm.jsx"], "sourcesContent": ["import React, { useState, useCallback, useEffect } from 'react';\nimport {\n  Box,\n  Container,\n  Typography,\n  Button,\n  Paper,\n  TextField,\n  Stack,\n  Avatar,\n  IconButton,\n  CircularProgress,\n  useTheme,\n  useMediaQuery,\n  InputAdornment,\n  Tooltip,\n  Alert,\n  FormControl,\n  FormControlLabel,\n  RadioGroup,\n  Radio,\n  Select,\n  MenuItem,\n  InputLabel,\n  Divider,\n  Snackbar\n} from '@mui/material';\nimport {\n  LocationOn,\n  MyLocation,\n  ArrowBack,\n  CloudUpload,\n  Delete,\n  ZoomIn,\n  ZoomOut\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { GoogleMap, useJsApiLoader, Marker } from '@react-google-maps/api';\nimport { modernMapStyle, markerIcons } from '../utils/mapStyles';\nimport { useAuth } from '../contexts/AuthContext';\nimport { suggestionsAPI } from '../services/api.service';\n\n// Google Maps container style\nconst mapContainerStyle = {\n  width: '100%',\n  height: '350px',\n  borderRadius: '12px'\n};\n\n// Default center (will be overridden by user's location)\nconst defaultCenter = {\n  lat: 40.7128, // New York\n  lng: -74.0060\n};\n\nconst libraries = [\"places\"];\n\nconst SuggestionForm = () => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const navigate = useNavigate();\n  const { currentUser } = useAuth();\n\n  const [address, setAddress] = useState('');\n  const [description, setDescription] = useState('');\n  const [photo, setPhoto] = useState(null);\n  const [photoPreview, setPhotoPreview] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isGettingLocation, setIsGettingLocation] = useState(false);\n  const [mapCenter, setMapCenter] = useState(defaultCenter);\n  const [markerPosition, setMarkerPosition] = useState(null);\n  const [mapZoom, setMapZoom] = useState(15);\n  const [mapError, setMapError] = useState('');\n  const [hasEV, setHasEV] = useState('');\n  const [weeklyUse, setWeeklyUse] = useState('');\n  const [error, setError] = useState('');\n  const [showError, setShowError] = useState(false);\n\n  // Load Google Maps API\n  const { isLoaded, loadError } = useJsApiLoader({\n    googleMapsApiKey: process.env.REACT_APP_GOOGLE_MAPS_API_KEY,\n    libraries\n  });\n\n  const handleBack = () => {\n    navigate('/');\n  };\n\n  // Handle map click to set marker\n  const handleMapClick = useCallback((event) => {\n    const lat = event.latLng.lat();\n    const lng = event.latLng.lng();\n\n    setMarkerPosition({ lat, lng });\n    setAddress(`Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}`);\n\n    // Optional: Use Google's Geocoding API to get the address from coordinates\n    // This would require additional API calls\n  }, []);\n\n  // Zoom controls\n  const handleZoomIn = () => {\n    setMapZoom(prev => Math.min(prev + 1, 20));\n  };\n\n  const handleZoomOut = () => {\n    setMapZoom(prev => Math.max(prev - 1, 1));\n  };\n\n  const handleGetCurrentLocation = () => {\n    setIsGettingLocation(true);\n    setMapError('');\n\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const lat = position.coords.latitude;\n          const lng = position.coords.longitude;\n\n          // Update map center and marker\n          setMapCenter({ lat, lng });\n          setMarkerPosition({ lat, lng });\n\n          // For demo purposes, just show the coordinates\n          setAddress(`Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}`);\n          setIsGettingLocation(false);\n        },\n        (error) => {\n          console.error('Error getting location:', error);\n          setIsGettingLocation(false);\n          setMapError('Unable to retrieve your location. Please enter the address manually or click on the map.');\n        }\n      );\n    } else {\n      setIsGettingLocation(false);\n      setMapError('Geolocation is not supported by your browser. Please enter the address manually or click on the map.');\n    }\n  };\n\n  const handlePhotoChange = (e) => {\n    if (e.target.files && e.target.files[0]) {\n      const selectedFile = e.target.files[0];\n      setPhoto(selectedFile);\n\n      // Create a preview\n      const reader = new FileReader();\n      reader.onload = (event) => {\n        setPhotoPreview(event.target.result);\n      };\n      reader.readAsDataURL(selectedFile);\n    }\n  };\n\n  const handleRemovePhoto = () => {\n    setPhoto(null);\n    setPhotoPreview('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!address.trim() || !markerPosition) {\n      setMapError('Please select a location on the map or use the current location button.');\n      return;\n    }\n\n    if (!hasEV) {\n      setError('Please indicate whether you own an electric vehicle.');\n      setShowError(true);\n      return;\n    }\n\n    if (!weeklyUse) {\n      setError('Please indicate how many times you would use this charging point weekly.');\n      setShowError(true);\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      // Create form data for submission\n      const formData = new FormData();\n      formData.append('address', address.trim());\n      formData.append('coordinates', JSON.stringify(markerPosition));\n      formData.append('description', description.trim());\n      formData.append('hasEV', hasEV === 'yes' ? 'true' : 'false');\n      formData.append('weeklyUse', weeklyUse.toString());\n\n      // Add photo if available\n      if (photo) {\n        formData.append('photo', photo);\n      }\n\n      // Send data to backend\n      const response = await suggestionsAPI.createSuggestion(formData);\n\n      if (response.status === 'success') {\n        // Navigate to confirmation page\n        navigate('/confirmation');\n      } else {\n        throw new Error(response.message || 'Failed to submit suggestion');\n      }\n    } catch (error) {\n      console.error('Submission error:', error);\n      setError(error.message || 'An error occurred while submitting your suggestion. Please try again.');\n      setShowError(true);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle error snackbar close\n  const handleErrorClose = () => {\n    setShowError(false);\n  };\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        bgcolor: '#F5F5F5',\n        pt: { xs: 8, md: 12 },\n        pb: 6\n      }}\n    >\n      {/* Error Snackbar */}\n      <Snackbar\n        open={showError}\n        autoHideDuration={6000}\n        onClose={handleErrorClose}\n        message={error}\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\n      />\n      <Container maxWidth=\"md\">\n        <IconButton\n          onClick={handleBack}\n          sx={{\n            mb: 2,\n            color: '#222222',\n            '&:hover': { bgcolor: 'rgba(46, 196, 182, 0.08)' }\n          }}\n        >\n          <ArrowBack />\n        </IconButton>\n\n        <Paper\n          elevation={0}\n          sx={{\n            p: { xs: 3, md: 5 },\n            borderRadius: 4,\n            bgcolor: 'white',\n            boxShadow: '0 8px 32px rgba(0,0,0,0.08)',\n            position: 'relative',\n            overflow: 'hidden'\n          }}\n        >\n          {/* Decorative Elements */}\n          <Box\n            sx={{\n              position: 'absolute',\n              width: '200px',\n              height: '200px',\n              borderRadius: '50%',\n              background: 'radial-gradient(circle, rgba(46,196,182,0.08) 0%, rgba(46,196,182,0) 70%)',\n              top: '-100px',\n              right: '-100px',\n              zIndex: 0\n            }}\n          />\n          <Box\n            sx={{\n              position: 'absolute',\n              width: '200px',\n              height: '200px',\n              borderRadius: '50%',\n              background: 'radial-gradient(circle, rgba(255,90,95,0.08) 0%, rgba(255,90,95,0) 70%)',\n              bottom: '-100px',\n              left: '-100px',\n              zIndex: 0\n            }}\n          />\n\n          <Box sx={{ position: 'relative', zIndex: 1 }}>\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1} sx={{ mb: 4 }}>\n              <LocationOn sx={{ color: '#FF5A5F', fontSize: 28 }} />\n              <Typography\n                variant=\"h5\"\n                sx={{\n                  fontWeight: 800,\n                  color: '#222222'\n                }}\n              >\n                Suggest a Charging Spot\n              </Typography>\n            </Stack>\n\n            <form onSubmit={handleSubmit}>\n              <Stack spacing={3}>\n                <Box>\n                  <Typography\n                    variant=\"subtitle1\"\n                    sx={{\n                      fontWeight: 600,\n                      mb: 1,\n                      color: '#222222'\n                    }}\n                  >\n                    Location\n                  </Typography>\n                  <TextField\n                    fullWidth\n                    placeholder=\"Enter address or use current location\"\n                    value={address}\n                    onChange={(e) => setAddress(e.target.value)}\n                    variant=\"outlined\"\n                    required\n                    InputProps={{\n                      startAdornment: (\n                        <InputAdornment position=\"start\">\n                          <LocationOn sx={{ color: '#2EC4B6' }} />\n                        </InputAdornment>\n                      ),\n                      endAdornment: (\n                        <InputAdornment position=\"end\">\n                          <Tooltip title=\"Use current location\">\n                            <IconButton\n                              onClick={handleGetCurrentLocation}\n                              disabled={isGettingLocation}\n                              sx={{ color: '#FF5A5F' }}\n                            >\n                              {isGettingLocation ? (\n                                <CircularProgress size={24} color=\"inherit\" />\n                              ) : (\n                                <MyLocation />\n                              )}\n                            </IconButton>\n                          </Tooltip>\n                        </InputAdornment>\n                      ),\n                      sx: {\n                        borderRadius: 2,\n                        bgcolor: '#F5F5F5',\n                        '&:hover': {\n                          bgcolor: '#F0F0F0'\n                        }\n                      }\n                    }}\n                  />\n\n                  {mapError && (\n                    <Alert\n                      severity=\"warning\"\n                      sx={{ mt: 2, borderRadius: 2 }}\n                    >\n                      {mapError}\n                    </Alert>\n                  )}\n\n                  {/* Google Map */}\n                  <Box sx={{ mt: 2, position: 'relative' }}>\n                    {isLoaded ? (\n                      <>\n                        <Paper\n                          elevation={0}\n                          sx={{\n                            borderRadius: 3,\n                            overflow: 'hidden',\n                            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n                            border: '1px solid rgba(0,0,0,0.05)',\n                          }}\n                        >\n                          <GoogleMap\n                            mapContainerStyle={mapContainerStyle}\n                            center={mapCenter}\n                            zoom={mapZoom}\n                            onClick={handleMapClick}\n                            options={{\n                              fullscreenControl: false,\n                              streetViewControl: false,\n                              mapTypeControl: false,\n                              zoomControl: false,\n                              styles: modernMapStyle,\n                              disableDefaultUI: true,\n                              gestureHandling: 'greedy',\n                            }}\n                          >\n                            {markerPosition && (\n                              <Marker\n                                position={markerPosition}\n                                animation={2} // DROP animation\n                                icon={markerIcons.selected}\n                              />\n                            )}\n                          </GoogleMap>\n                        </Paper>\n\n                        {/* Custom zoom controls */}\n                        <Box\n                          sx={{\n                            position: 'absolute',\n                            right: 16,\n                            top: 16,\n                            display: 'flex',\n                            flexDirection: 'column',\n                            bgcolor: 'white',\n                            borderRadius: 2,\n                            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                            p: 0.5,\n                            '& .MuiIconButton-root': {\n                              color: '#222222',\n                              transition: 'all 0.2s ease',\n                              '&:hover': {\n                                bgcolor: 'rgba(46, 196, 182, 0.1)',\n                                color: '#2EC4B6',\n                              }\n                            }\n                          }}\n                        >\n                          <IconButton onClick={handleZoomIn} size=\"small\">\n                            <ZoomIn />\n                          </IconButton>\n                          <Divider sx={{ my: 0.5 }} />\n                          <IconButton onClick={handleZoomOut} size=\"small\">\n                            <ZoomOut />\n                          </IconButton>\n                        </Box>\n                      </>\n                    ) : (\n                      <Paper\n                        elevation={0}\n                        sx={{\n                          height: 350,\n                          bgcolor: '#F5F5F5',\n                          borderRadius: 3,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n                          border: '1px solid rgba(0,0,0,0.05)',\n                        }}\n                      >\n                        {loadError ? (\n                          <Typography color=\"error\">\n                            Error loading maps. Please try again later.\n                          </Typography>\n                        ) : (\n                          <Box sx={{ textAlign: 'center' }}>\n                            <CircularProgress color=\"primary\" size={40} sx={{ mb: 2 }} />\n                            <Typography variant=\"body2\" color=\"text.secondary\">\n                              Loading map...\n                            </Typography>\n                          </Box>\n                        )}\n                      </Paper>\n                    )}\n                  </Box>\n\n                  <Typography\n                    variant=\"caption\"\n                    sx={{\n                      display: 'block',\n                      mt: 1,\n                      color: 'text.secondary',\n                      textAlign: 'center'\n                    }}\n                  >\n                    Click on the map to set a location or use the \"Current Location\" button\n                  </Typography>\n                </Box>\n\n                <Box>\n                  <Typography\n                    variant=\"subtitle1\"\n                    sx={{\n                      fontWeight: 600,\n                      mb: 1,\n                      color: '#222222'\n                    }}\n                  >\n                    Description (Optional)\n                  </Typography>\n                  <TextField\n                    fullWidth\n                    placeholder=\"Why is this a good location for a charging station?\"\n                    value={description}\n                    onChange={(e) => setDescription(e.target.value)}\n                    variant=\"outlined\"\n                    multiline\n                    rows={3}\n                    InputProps={{\n                      sx: {\n                        borderRadius: 2,\n                        bgcolor: '#F5F5F5',\n                        '&:hover': {\n                          bgcolor: '#F0F0F0'\n                        }\n                      }\n                    }}\n                  />\n                </Box>\n\n                <Box>\n                  <Typography\n                    variant=\"subtitle1\"\n                    sx={{\n                      fontWeight: 600,\n                      mb: 1,\n                      color: '#222222'\n                    }}\n                  >\n                    Do you own an electric vehicle?\n                  </Typography>\n                  <FormControl required sx={{ mb: 3 }}>\n                    <RadioGroup\n                      row\n                      value={hasEV}\n                      onChange={(e) => setHasEV(e.target.value)}\n                    >\n                      <FormControlLabel\n                        value=\"yes\"\n                        control={<Radio sx={{ color: '#2EC4B6', '&.Mui-checked': { color: '#2EC4B6' } }} />}\n                        label=\"Yes\"\n                      />\n                      <FormControlLabel\n                        value=\"no\"\n                        control={<Radio sx={{ color: '#2EC4B6', '&.Mui-checked': { color: '#2EC4B6' } }} />}\n                        label=\"No\"\n                      />\n                    </RadioGroup>\n                  </FormControl>\n                </Box>\n\n                <Box>\n                  <Typography\n                    variant=\"subtitle1\"\n                    sx={{\n                      fontWeight: 600,\n                      mb: 1,\n                      color: '#222222'\n                    }}\n                  >\n                    If there was a charging point here, how many times would you use it weekly?\n                  </Typography>\n                  <FormControl fullWidth required sx={{ mb: 3 }}>\n                    <Select\n                      value={weeklyUse}\n                      onChange={(e) => setWeeklyUse(e.target.value)}\n                      displayEmpty\n                      sx={{\n                        borderRadius: 2,\n                        bgcolor: '#F5F5F5',\n                        '&:hover': {\n                          bgcolor: '#F0F0F0'\n                        }\n                      }}\n                    >\n                      <MenuItem value=\"\" disabled>Select frequency</MenuItem>\n                      <MenuItem value=\"1\">1 time per week</MenuItem>\n                      <MenuItem value=\"2\">2 times per week</MenuItem>\n                      <MenuItem value=\"3\">3 times per week</MenuItem>\n                      <MenuItem value=\"4\">4 times per week</MenuItem>\n                      <MenuItem value=\"5\">5 or more times per week</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Box>\n\n                <Box>\n                  <Typography\n                    variant=\"subtitle1\"\n                    sx={{\n                      fontWeight: 600,\n                      mb: 1,\n                      color: '#222222'\n                    }}\n                  >\n                    Photo (Optional)\n                  </Typography>\n\n                  {!photoPreview ? (\n                    <Button\n                      component=\"label\"\n                      variant=\"outlined\"\n                      startIcon={<CloudUpload />}\n                      sx={{\n                        py: 1.5,\n                        px: 2,\n                        borderColor: 'rgba(0,0,0,0.12)',\n                        borderStyle: 'dashed',\n                        color: '#666666',\n                        borderRadius: 2,\n                        '&:hover': {\n                          borderColor: '#2EC4B6',\n                          bgcolor: 'rgba(46, 196, 182, 0.04)'\n                        }\n                      }}\n                    >\n                      Upload Photo\n                      <input\n                        type=\"file\"\n                        hidden\n                        accept=\"image/*\"\n                        onChange={handlePhotoChange}\n                      />\n                    </Button>\n                  ) : (\n                    <Box sx={{ position: 'relative', width: 'fit-content' }}>\n                      <Box\n                        component=\"img\"\n                        src={photoPreview}\n                        alt=\"Location preview\"\n                        sx={{\n                          width: '100%',\n                          maxWidth: 300,\n                          height: 'auto',\n                          borderRadius: 2,\n                          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'\n                        }}\n                      />\n                      <IconButton\n                        onClick={handleRemovePhoto}\n                        sx={{\n                          position: 'absolute',\n                          top: 8,\n                          right: 8,\n                          bgcolor: 'rgba(255,255,255,0.8)',\n                          color: '#FF5A5F',\n                          '&:hover': {\n                            bgcolor: 'rgba(255,255,255,0.9)',\n                          }\n                        }}\n                      >\n                        <Delete />\n                      </IconButton>\n                    </Box>\n                  )}\n                </Box>\n\n                <Button\n                  type=\"submit\"\n                  variant=\"contained\"\n                  size=\"large\"\n                  disabled={isLoading || !address.trim()}\n                  sx={{\n                    bgcolor: '#2EC4B6',\n                    color: 'white',\n                    py: 1.5,\n                    fontWeight: 700,\n                    fontSize: '1.1rem',\n                    borderRadius: 99,\n                    mt: 2,\n                    '&:hover': {\n                      bgcolor: '#23C3B1',\n                      transform: 'translateY(-2px)',\n                      boxShadow: '0 6px 20px rgba(46, 196, 182, 0.3)',\n                    },\n                    transition: 'all 0.3s ease'\n                  }}\n                >\n                  {isLoading ? (\n                    <CircularProgress size={24} color=\"inherit\" />\n                  ) : (\n                    'Submit Suggestion'\n                  )}\n                </Button>\n              </Stack>\n            </form>\n          </Box>\n        </Paper>\n      </Container>\n    </Box>\n  );\n};\n\nexport default SuggestionForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,CAAEC,SAAS,KAAQ,OAAO,CAC/D,OACEC,GAAG,CACHC,SAAS,CACTC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,SAAS,CACTC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,gBAAgB,CAChBC,QAAQ,CACRC,aAAa,CACbC,cAAc,CACdC,OAAO,CACPC,KAAK,CACLC,WAAW,CACXC,gBAAgB,CAChBC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,QAAQ,CACRC,UAAU,CACVC,OAAO,CACPC,QAAQ,KACH,eAAe,CACtB,OACEC,UAAU,CACVC,UAAU,CACVC,SAAS,CACTC,WAAW,CACXC,MAAM,CACNC,MAAM,CACNC,OAAO,KACF,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,SAAS,CAAEC,cAAc,CAAEC,MAAM,KAAQ,wBAAwB,CAC1E,OAASC,cAAc,CAAEC,WAAW,KAAQ,oBAAoB,CAChE,OAASC,OAAO,KAAQ,yBAAyB,CACjD,OAASC,cAAc,KAAQ,yBAAyB,CAExD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,iBAAiB,CAAG,CACxBC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,OAAO,CACfC,YAAY,CAAE,MAChB,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAG,CACpBC,GAAG,CAAE,OAAO,CAAE;AACdC,GAAG,CAAE,CAAC,OACR,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG,CAAC,QAAQ,CAAC,CAE5B,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,KAAK,CAAG5C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA6C,QAAQ,CAAG5C,aAAa,CAAC2C,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAAC,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAE4B,WAAY,CAAC,CAAGtB,OAAO,CAAC,CAAC,CAEjC,KAAM,CAACuB,OAAO,CAAEC,UAAU,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACiE,WAAW,CAAEC,cAAc,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACmE,KAAK,CAAEC,QAAQ,CAAC,CAAGpE,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACuE,SAAS,CAAEC,YAAY,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACyE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC2E,SAAS,CAAEC,YAAY,CAAC,CAAG5E,QAAQ,CAACoD,aAAa,CAAC,CACzD,KAAM,CAACyB,cAAc,CAAEC,iBAAiB,CAAC,CAAG9E,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC+E,OAAO,CAAEC,UAAU,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACiF,QAAQ,CAAEC,WAAW,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACmF,KAAK,CAAEC,QAAQ,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqF,SAAS,CAAEC,YAAY,CAAC,CAAGtF,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACuF,KAAK,CAAEC,QAAQ,CAAC,CAAGxF,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACyF,SAAS,CAAEC,YAAY,CAAC,CAAG1F,QAAQ,CAAC,KAAK,CAAC,CAEjD;AACA,KAAM,CAAE2F,QAAQ,CAAEC,SAAU,CAAC,CAAGxD,cAAc,CAAC,CAC7CyD,gBAAgB,CAAEC,OAAO,CAACC,GAAG,CAACC,6BAA6B,CAC3DzC,SACF,CAAC,CAAC,CAEF,KAAM,CAAA0C,UAAU,CAAGA,CAAA,GAAM,CACvBpC,QAAQ,CAAC,GAAG,CAAC,CACf,CAAC,CAED;AACA,KAAM,CAAAqC,cAAc,CAAGjG,WAAW,CAAEkG,KAAK,EAAK,CAC5C,KAAM,CAAA9C,GAAG,CAAG8C,KAAK,CAACC,MAAM,CAAC/C,GAAG,CAAC,CAAC,CAC9B,KAAM,CAAAC,GAAG,CAAG6C,KAAK,CAACC,MAAM,CAAC9C,GAAG,CAAC,CAAC,CAE9BwB,iBAAiB,CAAC,CAAEzB,GAAG,CAAEC,GAAI,CAAC,CAAC,CAC/BU,UAAU,SAAAqC,MAAA,CAAShD,GAAG,CAACiD,OAAO,CAAC,CAAC,CAAC,YAAAD,MAAA,CAAU/C,GAAG,CAACgD,OAAO,CAAC,CAAC,CAAC,CAAE,CAAC,CAE5D;AACA;AACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBvB,UAAU,CAACwB,IAAI,EAAIC,IAAI,CAACC,GAAG,CAACF,IAAI,CAAG,CAAC,CAAE,EAAE,CAAC,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAG,aAAa,CAAGA,CAAA,GAAM,CAC1B3B,UAAU,CAACwB,IAAI,EAAIC,IAAI,CAACG,GAAG,CAACJ,IAAI,CAAG,CAAC,CAAE,CAAC,CAAC,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAK,wBAAwB,CAAGA,CAAA,GAAM,CACrCnC,oBAAoB,CAAC,IAAI,CAAC,CAC1BQ,WAAW,CAAC,EAAE,CAAC,CAEf,GAAI4B,SAAS,CAACC,WAAW,CAAE,CACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,EAAK,CACZ,KAAM,CAAA5D,GAAG,CAAG4D,QAAQ,CAACC,MAAM,CAACC,QAAQ,CACpC,KAAM,CAAA7D,GAAG,CAAG2D,QAAQ,CAACC,MAAM,CAACE,SAAS,CAErC;AACAxC,YAAY,CAAC,CAAEvB,GAAG,CAAEC,GAAI,CAAC,CAAC,CAC1BwB,iBAAiB,CAAC,CAAEzB,GAAG,CAAEC,GAAI,CAAC,CAAC,CAE/B;AACAU,UAAU,SAAAqC,MAAA,CAAShD,GAAG,CAACiD,OAAO,CAAC,CAAC,CAAC,YAAAD,MAAA,CAAU/C,GAAG,CAACgD,OAAO,CAAC,CAAC,CAAC,CAAE,CAAC,CAC5D5B,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CACAa,KAAK,EAAK,CACT8B,OAAO,CAAC9B,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/Cb,oBAAoB,CAAC,KAAK,CAAC,CAC3BQ,WAAW,CAAC,0FAA0F,CAAC,CACzG,CACF,CAAC,CACH,CAAC,IAAM,CACLR,oBAAoB,CAAC,KAAK,CAAC,CAC3BQ,WAAW,CAAC,sGAAsG,CAAC,CACrH,CACF,CAAC,CAED,KAAM,CAAAoC,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,GAAIA,CAAC,CAACC,MAAM,CAACC,KAAK,EAAIF,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAE,CACvC,KAAM,CAAAC,YAAY,CAAGH,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CACtCrD,QAAQ,CAACsD,YAAY,CAAC,CAEtB;AACA,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAI1B,KAAK,EAAK,CACzB7B,eAAe,CAAC6B,KAAK,CAACqB,MAAM,CAACM,MAAM,CAAC,CACtC,CAAC,CACDH,MAAM,CAACI,aAAa,CAACL,YAAY,CAAC,CACpC,CACF,CAAC,CAED,KAAM,CAAAM,iBAAiB,CAAGA,CAAA,GAAM,CAC9B5D,QAAQ,CAAC,IAAI,CAAC,CACdE,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,CAED,KAAM,CAAA2D,YAAY,CAAG,KAAO,CAAAV,CAAC,EAAK,CAChCA,CAAC,CAACW,cAAc,CAAC,CAAC,CAElB,GAAI,CAACnE,OAAO,CAACoE,IAAI,CAAC,CAAC,EAAI,CAACtD,cAAc,CAAE,CACtCK,WAAW,CAAC,yEAAyE,CAAC,CACtF,OACF,CAEA,GAAI,CAACC,KAAK,CAAE,CACVK,QAAQ,CAAC,sDAAsD,CAAC,CAChEE,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CAEA,GAAI,CAACL,SAAS,CAAE,CACdG,QAAQ,CAAC,0EAA0E,CAAC,CACpFE,YAAY,CAAC,IAAI,CAAC,CAClB,OACF,CAEAlB,YAAY,CAAC,IAAI,CAAC,CAClBgB,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF;AACA,KAAM,CAAA4C,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,CAAEvE,OAAO,CAACoE,IAAI,CAAC,CAAC,CAAC,CAC1CC,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAEC,IAAI,CAACC,SAAS,CAAC3D,cAAc,CAAC,CAAC,CAC9DuD,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAErE,WAAW,CAACkE,IAAI,CAAC,CAAC,CAAC,CAClDC,QAAQ,CAACE,MAAM,CAAC,OAAO,CAAEnD,KAAK,GAAK,KAAK,CAAG,MAAM,CAAG,OAAO,CAAC,CAC5DiD,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAEjD,SAAS,CAACoD,QAAQ,CAAC,CAAC,CAAC,CAElD;AACA,GAAItE,KAAK,CAAE,CACTiE,QAAQ,CAACE,MAAM,CAAC,OAAO,CAAEnE,KAAK,CAAC,CACjC,CAEA;AACA,KAAM,CAAAuE,QAAQ,CAAG,KAAM,CAAAjG,cAAc,CAACkG,gBAAgB,CAACP,QAAQ,CAAC,CAEhE,GAAIM,QAAQ,CAACE,MAAM,GAAK,SAAS,CAAE,CACjC;AACA/E,QAAQ,CAAC,eAAe,CAAC,CAC3B,CAAC,IAAM,CACL,KAAM,IAAI,CAAAgF,KAAK,CAACH,QAAQ,CAACI,OAAO,EAAI,6BAA6B,CAAC,CACpE,CACF,CAAE,MAAOvD,KAAK,CAAE,CACd8B,OAAO,CAAC9B,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzCC,QAAQ,CAACD,KAAK,CAACuD,OAAO,EAAI,uEAAuE,CAAC,CAClGpD,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,OAAS,CACRlB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAuE,gBAAgB,CAAGA,CAAA,GAAM,CAC7BrD,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,mBACE7C,KAAA,CAAC1C,GAAG,EACF6I,EAAE,CAAE,CACFC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,SAAS,CAClBC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,EAAG,CAAC,CACrBC,EAAE,CAAE,CACN,CAAE,CAAAC,QAAA,eAGF5G,IAAA,CAACjB,QAAQ,EACP8H,IAAI,CAAE/D,SAAU,CAChBgE,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEX,gBAAiB,CAC1BD,OAAO,CAAEvD,KAAM,CACfoE,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAE,CACzD,CAAC,cACFhH,KAAA,CAACzC,SAAS,EAAC0J,QAAQ,CAAC,IAAI,CAAAP,QAAA,eACtB5G,IAAA,CAAChC,UAAU,EACToJ,OAAO,CAAE9D,UAAW,CACpB+C,EAAE,CAAE,CACFgB,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SAAS,CAChB,SAAS,CAAE,CAAEf,OAAO,CAAE,0BAA2B,CACnD,CAAE,CAAAK,QAAA,cAEF5G,IAAA,CAACd,SAAS,GAAE,CAAC,CACH,CAAC,cAEbgB,KAAA,CAACtC,KAAK,EACJ2J,SAAS,CAAE,CAAE,CACblB,EAAE,CAAE,CACFmB,CAAC,CAAE,CAAEf,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBlG,YAAY,CAAE,CAAC,CACf+F,OAAO,CAAE,OAAO,CAChBkB,SAAS,CAAE,6BAA6B,CACxCnD,QAAQ,CAAE,UAAU,CACpBoD,QAAQ,CAAE,QACZ,CAAE,CAAAd,QAAA,eAGF5G,IAAA,CAACxC,GAAG,EACF6I,EAAE,CAAE,CACF/B,QAAQ,CAAE,UAAU,CACpBhE,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,OAAO,CACfC,YAAY,CAAE,KAAK,CACnBmH,UAAU,CAAE,2EAA2E,CACvFC,GAAG,CAAE,QAAQ,CACbC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACF9H,IAAA,CAACxC,GAAG,EACF6I,EAAE,CAAE,CACF/B,QAAQ,CAAE,UAAU,CACpBhE,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,OAAO,CACfC,YAAY,CAAE,KAAK,CACnBmH,UAAU,CAAE,yEAAyE,CACrFI,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,QAAQ,CACdF,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cAEF5H,KAAA,CAAC1C,GAAG,EAAC6I,EAAE,CAAE,CAAE/B,QAAQ,CAAE,UAAU,CAAEwD,MAAM,CAAE,CAAE,CAAE,CAAAlB,QAAA,eAC3C1G,KAAA,CAACpC,KAAK,EAACmK,SAAS,CAAC,KAAK,CAACC,UAAU,CAAC,QAAQ,CAACC,OAAO,CAAE,CAAE,CAAC9B,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,eACnE5G,IAAA,CAAChB,UAAU,EAACqH,EAAE,CAAE,CAAEiB,KAAK,CAAE,SAAS,CAAEc,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,cACtDpI,IAAA,CAACtC,UAAU,EACT2K,OAAO,CAAC,IAAI,CACZhC,EAAE,CAAE,CACFiC,UAAU,CAAE,GAAG,CACfhB,KAAK,CAAE,SACT,CAAE,CAAAV,QAAA,CACH,yBAED,CAAY,CAAC,EACR,CAAC,cAER5G,IAAA,SAAMuI,QAAQ,CAAEjD,YAAa,CAAAsB,QAAA,cAC3B1G,KAAA,CAACpC,KAAK,EAACqK,OAAO,CAAE,CAAE,CAAAvB,QAAA,eAChB1G,KAAA,CAAC1C,GAAG,EAAAoJ,QAAA,eACF5G,IAAA,CAACtC,UAAU,EACT2K,OAAO,CAAC,WAAW,CACnBhC,EAAE,CAAE,CACFiC,UAAU,CAAE,GAAG,CACfjB,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SACT,CAAE,CAAAV,QAAA,CACH,UAED,CAAY,CAAC,cACb5G,IAAA,CAACnC,SAAS,EACR2K,SAAS,MACTC,WAAW,CAAC,uCAAuC,CACnDC,KAAK,CAAEtH,OAAQ,CACfuH,QAAQ,CAAG/D,CAAC,EAAKvD,UAAU,CAACuD,CAAC,CAACC,MAAM,CAAC6D,KAAK,CAAE,CAC5CL,OAAO,CAAC,UAAU,CAClBO,QAAQ,MACRC,UAAU,CAAE,CACVC,cAAc,cACZ9I,IAAA,CAAC5B,cAAc,EAACkG,QAAQ,CAAC,OAAO,CAAAsC,QAAA,cAC9B5G,IAAA,CAAChB,UAAU,EAACqH,EAAE,CAAE,CAAEiB,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CAC1B,CACjB,CACDyB,YAAY,cACV/I,IAAA,CAAC5B,cAAc,EAACkG,QAAQ,CAAC,KAAK,CAAAsC,QAAA,cAC5B5G,IAAA,CAAC3B,OAAO,EAAC2K,KAAK,CAAC,sBAAsB,CAAApC,QAAA,cACnC5G,IAAA,CAAChC,UAAU,EACToJ,OAAO,CAAElD,wBAAyB,CAClC+E,QAAQ,CAAEnH,iBAAkB,CAC5BuE,EAAE,CAAE,CAAEiB,KAAK,CAAE,SAAU,CAAE,CAAAV,QAAA,CAExB9E,iBAAiB,cAChB9B,IAAA,CAAC/B,gBAAgB,EAACiL,IAAI,CAAE,EAAG,CAAC5B,KAAK,CAAC,SAAS,CAAE,CAAC,cAE9CtH,IAAA,CAACf,UAAU,GAAE,CACd,CACS,CAAC,CACN,CAAC,CACI,CACjB,CACDoH,EAAE,CAAE,CACF7F,YAAY,CAAE,CAAC,CACf+F,OAAO,CAAE,SAAS,CAClB,SAAS,CAAE,CACTA,OAAO,CAAE,SACX,CACF,CACF,CAAE,CACH,CAAC,CAEDjE,QAAQ,eACPtC,IAAA,CAAC1B,KAAK,EACJ6K,QAAQ,CAAC,SAAS,CAClB9C,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAC,CAAE5I,YAAY,CAAE,CAAE,CAAE,CAAAoG,QAAA,CAE9BtE,QAAQ,CACJ,CACR,cAGDtC,IAAA,CAACxC,GAAG,EAAC6I,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAC,CAAE9E,QAAQ,CAAE,UAAW,CAAE,CAAAsC,QAAA,CACtC5D,QAAQ,cACP9C,KAAA,CAAAE,SAAA,EAAAwG,QAAA,eACE5G,IAAA,CAACpC,KAAK,EACJ2J,SAAS,CAAE,CAAE,CACblB,EAAE,CAAE,CACF7F,YAAY,CAAE,CAAC,CACfkH,QAAQ,CAAE,QAAQ,CAClBD,SAAS,CAAE,6BAA6B,CACxC4B,MAAM,CAAE,4BACV,CAAE,CAAAzC,QAAA,cAEF5G,IAAA,CAACR,SAAS,EACRa,iBAAiB,CAAEA,iBAAkB,CACrCiJ,MAAM,CAAEtH,SAAU,CAClBuH,IAAI,CAAEnH,OAAQ,CACdgF,OAAO,CAAE7D,cAAe,CACxBiG,OAAO,CAAE,CACPC,iBAAiB,CAAE,KAAK,CACxBC,iBAAiB,CAAE,KAAK,CACxBC,cAAc,CAAE,KAAK,CACrBC,WAAW,CAAE,KAAK,CAClBC,MAAM,CAAElK,cAAc,CACtBmK,gBAAgB,CAAE,IAAI,CACtBC,eAAe,CAAE,QACnB,CAAE,CAAAnD,QAAA,CAED1E,cAAc,eACblC,IAAA,CAACN,MAAM,EACL4E,QAAQ,CAAEpC,cAAe,CACzB8H,SAAS,CAAE,CAAG;AAAA,CACdC,IAAI,CAAErK,WAAW,CAACsK,QAAS,CAC5B,CACF,CACQ,CAAC,CACP,CAAC,cAGRhK,KAAA,CAAC1C,GAAG,EACF6I,EAAE,CAAE,CACF/B,QAAQ,CAAE,UAAU,CACpBuD,KAAK,CAAE,EAAE,CACTD,GAAG,CAAE,EAAE,CACPuC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB7D,OAAO,CAAE,OAAO,CAChB/F,YAAY,CAAE,CAAC,CACfiH,SAAS,CAAE,6BAA6B,CACxCD,CAAC,CAAE,GAAG,CACN,uBAAuB,CAAE,CACvBF,KAAK,CAAE,SAAS,CAChB+C,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACT9D,OAAO,CAAE,yBAAyB,CAClCe,KAAK,CAAE,SACT,CACF,CACF,CAAE,CAAAV,QAAA,eAEF5G,IAAA,CAAChC,UAAU,EAACoJ,OAAO,CAAExD,YAAa,CAACsF,IAAI,CAAC,OAAO,CAAAtC,QAAA,cAC7C5G,IAAA,CAACX,MAAM,GAAE,CAAC,CACA,CAAC,cACbW,IAAA,CAAClB,OAAO,EAACuH,EAAE,CAAE,CAAEiE,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cAC5BtK,IAAA,CAAChC,UAAU,EAACoJ,OAAO,CAAEpD,aAAc,CAACkF,IAAI,CAAC,OAAO,CAAAtC,QAAA,cAC9C5G,IAAA,CAACV,OAAO,GAAE,CAAC,CACD,CAAC,EACV,CAAC,EACN,CAAC,cAEHU,IAAA,CAACpC,KAAK,EACJ2J,SAAS,CAAE,CAAE,CACblB,EAAE,CAAE,CACF9F,MAAM,CAAE,GAAG,CACXgG,OAAO,CAAE,SAAS,CAClB/F,YAAY,CAAE,CAAC,CACf2J,OAAO,CAAE,MAAM,CACfjC,UAAU,CAAE,QAAQ,CACpBqC,cAAc,CAAE,QAAQ,CACxB9C,SAAS,CAAE,6BAA6B,CACxC4B,MAAM,CAAE,4BACV,CAAE,CAAAzC,QAAA,CAED3D,SAAS,cACRjD,IAAA,CAACtC,UAAU,EAAC4J,KAAK,CAAC,OAAO,CAAAV,QAAA,CAAC,6CAE1B,CAAY,CAAC,cAEb1G,KAAA,CAAC1C,GAAG,EAAC6I,EAAE,CAAE,CAAEmE,SAAS,CAAE,QAAS,CAAE,CAAA5D,QAAA,eAC/B5G,IAAA,CAAC/B,gBAAgB,EAACqJ,KAAK,CAAC,SAAS,CAAC4B,IAAI,CAAE,EAAG,CAAC7C,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC7DrH,IAAA,CAACtC,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAACf,KAAK,CAAC,gBAAgB,CAAAV,QAAA,CAAC,gBAEnD,CAAY,CAAC,EACV,CACN,CACI,CACR,CACE,CAAC,cAEN5G,IAAA,CAACtC,UAAU,EACT2K,OAAO,CAAC,SAAS,CACjBhC,EAAE,CAAE,CACF8D,OAAO,CAAE,OAAO,CAChBf,EAAE,CAAE,CAAC,CACL9B,KAAK,CAAE,gBAAgB,CACvBkD,SAAS,CAAE,QACb,CAAE,CAAA5D,QAAA,CACH,2EAED,CAAY,CAAC,EACV,CAAC,cAEN1G,KAAA,CAAC1C,GAAG,EAAAoJ,QAAA,eACF5G,IAAA,CAACtC,UAAU,EACT2K,OAAO,CAAC,WAAW,CACnBhC,EAAE,CAAE,CACFiC,UAAU,CAAE,GAAG,CACfjB,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SACT,CAAE,CAAAV,QAAA,CACH,wBAED,CAAY,CAAC,cACb5G,IAAA,CAACnC,SAAS,EACR2K,SAAS,MACTC,WAAW,CAAC,qDAAqD,CACjEC,KAAK,CAAEpH,WAAY,CACnBqH,QAAQ,CAAG/D,CAAC,EAAKrD,cAAc,CAACqD,CAAC,CAACC,MAAM,CAAC6D,KAAK,CAAE,CAChDL,OAAO,CAAC,UAAU,CAClBoC,SAAS,MACTC,IAAI,CAAE,CAAE,CACR7B,UAAU,CAAE,CACVxC,EAAE,CAAE,CACF7F,YAAY,CAAE,CAAC,CACf+F,OAAO,CAAE,SAAS,CAClB,SAAS,CAAE,CACTA,OAAO,CAAE,SACX,CACF,CACF,CAAE,CACH,CAAC,EACC,CAAC,cAENrG,KAAA,CAAC1C,GAAG,EAAAoJ,QAAA,eACF5G,IAAA,CAACtC,UAAU,EACT2K,OAAO,CAAC,WAAW,CACnBhC,EAAE,CAAE,CACFiC,UAAU,CAAE,GAAG,CACfjB,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SACT,CAAE,CAAAV,QAAA,CACH,iCAED,CAAY,CAAC,cACb5G,IAAA,CAACzB,WAAW,EAACqK,QAAQ,MAACvC,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,cAClC1G,KAAA,CAACzB,UAAU,EACTkM,GAAG,MACHjC,KAAK,CAAElG,KAAM,CACbmG,QAAQ,CAAG/D,CAAC,EAAKnC,QAAQ,CAACmC,CAAC,CAACC,MAAM,CAAC6D,KAAK,CAAE,CAAA9B,QAAA,eAE1C5G,IAAA,CAACxB,gBAAgB,EACfkK,KAAK,CAAC,KAAK,CACXkC,OAAO,cAAE5K,IAAA,CAACtB,KAAK,EAAC2H,EAAE,CAAE,CAAEiB,KAAK,CAAE,SAAS,CAAE,eAAe,CAAE,CAAEA,KAAK,CAAE,SAAU,CAAE,CAAE,CAAE,CAAE,CACpFuD,KAAK,CAAC,KAAK,CACZ,CAAC,cACF7K,IAAA,CAACxB,gBAAgB,EACfkK,KAAK,CAAC,IAAI,CACVkC,OAAO,cAAE5K,IAAA,CAACtB,KAAK,EAAC2H,EAAE,CAAE,CAAEiB,KAAK,CAAE,SAAS,CAAE,eAAe,CAAE,CAAEA,KAAK,CAAE,SAAU,CAAE,CAAE,CAAE,CAAE,CACpFuD,KAAK,CAAC,IAAI,CACX,CAAC,EACQ,CAAC,CACF,CAAC,EACX,CAAC,cAEN3K,KAAA,CAAC1C,GAAG,EAAAoJ,QAAA,eACF5G,IAAA,CAACtC,UAAU,EACT2K,OAAO,CAAC,WAAW,CACnBhC,EAAE,CAAE,CACFiC,UAAU,CAAE,GAAG,CACfjB,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SACT,CAAE,CAAAV,QAAA,CACH,6EAED,CAAY,CAAC,cACb5G,IAAA,CAACzB,WAAW,EAACiK,SAAS,MAACI,QAAQ,MAACvC,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,cAC5C1G,KAAA,CAACvB,MAAM,EACL+J,KAAK,CAAEhG,SAAU,CACjBiG,QAAQ,CAAG/D,CAAC,EAAKjC,YAAY,CAACiC,CAAC,CAACC,MAAM,CAAC6D,KAAK,CAAE,CAC9CoC,YAAY,MACZzE,EAAE,CAAE,CACF7F,YAAY,CAAE,CAAC,CACf+F,OAAO,CAAE,SAAS,CAClB,SAAS,CAAE,CACTA,OAAO,CAAE,SACX,CACF,CAAE,CAAAK,QAAA,eAEF5G,IAAA,CAACpB,QAAQ,EAAC8J,KAAK,CAAC,EAAE,CAACO,QAAQ,MAAArC,QAAA,CAAC,kBAAgB,CAAU,CAAC,cACvD5G,IAAA,CAACpB,QAAQ,EAAC8J,KAAK,CAAC,GAAG,CAAA9B,QAAA,CAAC,iBAAe,CAAU,CAAC,cAC9C5G,IAAA,CAACpB,QAAQ,EAAC8J,KAAK,CAAC,GAAG,CAAA9B,QAAA,CAAC,kBAAgB,CAAU,CAAC,cAC/C5G,IAAA,CAACpB,QAAQ,EAAC8J,KAAK,CAAC,GAAG,CAAA9B,QAAA,CAAC,kBAAgB,CAAU,CAAC,cAC/C5G,IAAA,CAACpB,QAAQ,EAAC8J,KAAK,CAAC,GAAG,CAAA9B,QAAA,CAAC,kBAAgB,CAAU,CAAC,cAC/C5G,IAAA,CAACpB,QAAQ,EAAC8J,KAAK,CAAC,GAAG,CAAA9B,QAAA,CAAC,0BAAwB,CAAU,CAAC,EACjD,CAAC,CACE,CAAC,EACX,CAAC,cAEN1G,KAAA,CAAC1C,GAAG,EAAAoJ,QAAA,eACF5G,IAAA,CAACtC,UAAU,EACT2K,OAAO,CAAC,WAAW,CACnBhC,EAAE,CAAE,CACFiC,UAAU,CAAE,GAAG,CACfjB,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,SACT,CAAE,CAAAV,QAAA,CACH,kBAED,CAAY,CAAC,CAEZ,CAAClF,YAAY,cACZxB,KAAA,CAACvC,MAAM,EACLoN,SAAS,CAAC,OAAO,CACjB1C,OAAO,CAAC,UAAU,CAClB2C,SAAS,cAAEhL,IAAA,CAACb,WAAW,GAAE,CAAE,CAC3BkH,EAAE,CAAE,CACF4E,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,CAAC,CACLC,WAAW,CAAE,kBAAkB,CAC/BC,WAAW,CAAE,QAAQ,CACrB9D,KAAK,CAAE,SAAS,CAChB9G,YAAY,CAAE,CAAC,CACf,SAAS,CAAE,CACT2K,WAAW,CAAE,SAAS,CACtB5E,OAAO,CAAE,0BACX,CACF,CAAE,CAAAK,QAAA,EACH,cAEC,cAAA5G,IAAA,UACEqL,IAAI,CAAC,MAAM,CACXC,MAAM,MACNC,MAAM,CAAC,SAAS,CAChB5C,QAAQ,CAAEhE,iBAAkB,CAC7B,CAAC,EACI,CAAC,cAETzE,KAAA,CAAC1C,GAAG,EAAC6I,EAAE,CAAE,CAAE/B,QAAQ,CAAE,UAAU,CAAEhE,KAAK,CAAE,aAAc,CAAE,CAAAsG,QAAA,eACtD5G,IAAA,CAACxC,GAAG,EACFuN,SAAS,CAAC,KAAK,CACfS,GAAG,CAAE9J,YAAa,CAClB+J,GAAG,CAAC,kBAAkB,CACtBpF,EAAE,CAAE,CACF/F,KAAK,CAAE,MAAM,CACb6G,QAAQ,CAAE,GAAG,CACb5G,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,CAAC,CACfiH,SAAS,CAAE,4BACb,CAAE,CACH,CAAC,cACFzH,IAAA,CAAChC,UAAU,EACToJ,OAAO,CAAE/B,iBAAkB,CAC3BgB,EAAE,CAAE,CACF/B,QAAQ,CAAE,UAAU,CACpBsD,GAAG,CAAE,CAAC,CACNC,KAAK,CAAE,CAAC,CACRtB,OAAO,CAAE,uBAAuB,CAChCe,KAAK,CAAE,SAAS,CAChB,SAAS,CAAE,CACTf,OAAO,CAAE,uBACX,CACF,CAAE,CAAAK,QAAA,cAEF5G,IAAA,CAACZ,MAAM,GAAE,CAAC,CACA,CAAC,EACV,CACN,EACE,CAAC,cAENY,IAAA,CAACrC,MAAM,EACL0N,IAAI,CAAC,QAAQ,CACbhD,OAAO,CAAC,WAAW,CACnBa,IAAI,CAAC,OAAO,CACZD,QAAQ,CAAErH,SAAS,EAAI,CAACR,OAAO,CAACoE,IAAI,CAAC,CAAE,CACvCa,EAAE,CAAE,CACFE,OAAO,CAAE,SAAS,CAClBe,KAAK,CAAE,OAAO,CACd2D,EAAE,CAAE,GAAG,CACP3C,UAAU,CAAE,GAAG,CACfF,QAAQ,CAAE,QAAQ,CAClB5H,YAAY,CAAE,EAAE,CAChB4I,EAAE,CAAE,CAAC,CACL,SAAS,CAAE,CACT7C,OAAO,CAAE,SAAS,CAClBmF,SAAS,CAAE,kBAAkB,CAC7BjE,SAAS,CAAE,oCACb,CAAC,CACD4C,UAAU,CAAE,eACd,CAAE,CAAAzD,QAAA,CAEDhF,SAAS,cACR5B,IAAA,CAAC/B,gBAAgB,EAACiL,IAAI,CAAE,EAAG,CAAC5B,KAAK,CAAC,SAAS,CAAE,CAAC,CAE9C,mBACD,CACK,CAAC,EACJ,CAAC,CACJ,CAAC,EACJ,CAAC,EACD,CAAC,EACC,CAAC,EACT,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}