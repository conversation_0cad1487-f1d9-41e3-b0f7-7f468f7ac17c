/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { FirebaseApp } from '@firebase/app';
import { ListenProvider } from '../../src/core/SyncTree';
import { Path } from '../../src/core/util/Path';
export declare class SyncPointTestParser {
    app: FirebaseApp;
    listens_: any;
    listenProvider_: ListenProvider;
    private syncTree_;
    constructor();
    getTestPath(optBasePath: string | string[], path?: string): Path;
    private testRunner;
    defineTest(spec: any): void;
}
