/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Operation } from '../operation/Operation';
import { Node } from '../snap/Node';
import { WriteTreeRef } from '../WriteTree';
import { Change } from './Change';
import { NodeFilter } from './filter/NodeFilter';
import { ViewCache } from './ViewCache';
export interface ProcessorResult {
    readonly viewCache: ViewCache;
    readonly changes: Change[];
}
export interface ViewProcessor {
    readonly filter: NodeFilter;
}
export declare function newViewProcessor(filter: NodeFilter): ViewProcessor;
export declare function viewProcessorAssertIndexed(viewProcessor: ViewProcessor, viewCache: ViewCache): void;
export declare function viewProcessorApplyOperation(viewProcessor: ViewProcessor, oldViewCache: ViewCache, operation: Operation, writesCache: WriteTreeRef, completeCache: Node | null): ProcessorResult;
