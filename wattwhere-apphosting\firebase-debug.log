[debug] [2025-08-24T16:57:26.665Z] ----------------------------------------------------------------------
[debug] [2025-08-24T16:57:26.667Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js apphosting:backends:create
[debug] [2025-08-24T16:57:26.667Z] CLI Version:   14.13.0
[debug] [2025-08-24T16:57:26.668Z] Platform:      win32
[debug] [2025-08-24T16:57:26.668Z] Node Version:  v22.10.0
[debug] [2025-08-24T16:57:26.668Z] Time:          Sun Aug 24 2025 19:57:26 GMT+0300 (Eastern European Summer Time)
[debug] [2025-08-24T16:57:26.668Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-24T16:57:26.827Z] Object "/apphosting" in "firebase.json" is missing required property: {"missingProperty":"backendId"}
[debug] [2025-08-24T16:57:26.827Z] Field "/apphosting" in "firebase.json" is possibly invalid: must be array
[debug] [2025-08-24T16:57:26.827Z] Field "/apphosting" in "firebase.json" is possibly invalid: must match a schema in anyOf
[debug] [2025-08-24T16:57:26.830Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-24T16:57:26.831Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-24T16:57:26.832Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:26.832Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:26.833Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-08-24T16:57:26.833Z] >>> [apiv2][(partial)header] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions x-goog-quota-user=projects/wattwhere-f8f65
[debug] [2025-08-24T16:57:26.833Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-08-24T16:57:30.461Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-08-24T16:57:30.461Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-08-24T16:57:30.461Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:30.462Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:30.462Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts [none]
[debug] [2025-08-24T16:57:30.462Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts {"accountId":"firebase-app-hosting-compute","serviceAccount":{"displayName":"Firebase App Hosting compute service account","description":"Default service account used to run builds and deploys for Firebase App Hosting"}}
[debug] [2025-08-24T16:57:31.972Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts 409
[debug] [2025-08-24T16:57:31.972Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts [omitted]
[debug] [2025-08-24T16:57:31.973Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:31.973Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:31.973Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy [none]
[debug] [2025-08-24T16:57:32.954Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy 200
[debug] [2025-08-24T16:57:32.955Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy {"version":1,"etag":"BwY9H0j0nXc=","bindings":[{"role":"roles/artifactregistry.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudaicompanion.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.builds.builder","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.developer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/containerregistry.ServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.readTokenAccessor","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/editor","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebase.managementServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.sdkAdminServiceAgent","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.computeRunner","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseauth.admin","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebasehosting.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasemods.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaserules.system","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firestore.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/iam.serviceAccountTokenCreator","members":["serviceAccount:<EMAIL>"]},{"role":"roles/owner","members":["user:<EMAIL>","user:<EMAIL>"]},{"role":"roles/run.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/run.viewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin_withcond_6a995354d163d6fe7b02","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.apiKeysViewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.serviceUsageConsumer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.objectViewer","members":["serviceAccount:<EMAIL>"]}]}
[debug] [2025-08-24T16:57:32.955Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:32.955Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:32.955Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:setIamPolicy [none]
[debug] [2025-08-24T16:57:32.955Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:setIamPolicy {"policy":{"version":1,"etag":"BwY9H0j0nXc=","bindings":[{"role":"roles/artifactregistry.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudaicompanion.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.builds.builder","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.developer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/containerregistry.ServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.readTokenAccessor","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/editor","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebase.managementServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.sdkAdminServiceAgent","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.computeRunner","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseauth.admin","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebasehosting.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasemods.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaserules.system","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firestore.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/iam.serviceAccountTokenCreator","members":["serviceAccount:<EMAIL>"]},{"role":"roles/owner","members":["user:<EMAIL>","user:<EMAIL>"]},{"role":"roles/run.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/run.viewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin_withcond_6a995354d163d6fe7b02","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.apiKeysViewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.serviceUsageConsumer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.objectViewer","members":["serviceAccount:<EMAIL>"]}]},"updateMask":"bindings"}
[debug] [2025-08-24T16:57:34.131Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:setIamPolicy 200
[debug] [2025-08-24T16:57:34.131Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:setIamPolicy {"version":1,"etag":"BwY9H1XjARE=","bindings":[{"role":"roles/artifactregistry.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudaicompanion.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.builds.builder","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.developer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/containerregistry.ServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.readTokenAccessor","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/editor","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebase.managementServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.sdkAdminServiceAgent","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.computeRunner","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseauth.admin","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebasehosting.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasemods.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaserules.system","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firestore.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/iam.serviceAccountTokenCreator","members":["serviceAccount:<EMAIL>"]},{"role":"roles/owner","members":["user:<EMAIL>","user:<EMAIL>"]},{"role":"roles/run.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/run.viewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin_withcond_6a995354d163d6fe7b02","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.apiKeysViewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.serviceUsageConsumer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.objectViewer","members":["serviceAccount:<EMAIL>"]}]}
[debug] [2025-08-24T16:57:34.132Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:34.132Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:57:34.132Z] >>> [apiv2][query] GET https://firebaseapphosting.googleapis.com/v1beta/projects/wattwhere-f8f65/locations 
[debug] [2025-08-24T16:57:35.545Z] <<< [apiv2][status] GET https://firebaseapphosting.googleapis.com/v1beta/projects/wattwhere-f8f65/locations 200
[debug] [2025-08-24T16:57:35.545Z] <<< [apiv2][body] GET https://firebaseapphosting.googleapis.com/v1beta/projects/wattwhere-f8f65/locations {"locations":[{"name":"projects/wattwhere-f8f65/locations/asia-east1","locationId":"asia-east1","displayName":"Taiwan"},{"name":"projects/wattwhere-f8f65/locations/europe-west4","locationId":"europe-west4","displayName":"Netherlands"},{"name":"projects/wattwhere-f8f65/locations/us-central1","locationId":"us-central1","displayName":"Iowa"}]}
[info] +  Location set to us-central1.
 
[info] i  === Import a GitHub repository 
[debug] [2025-08-24T16:59:30.650Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:30.650Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:30.651Z] >>> [apiv2][query] GET https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/connections pageSize=1000&pageToken=
[debug] [2025-08-24T16:59:31.721Z] <<< [apiv2][status] GET https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/connections 200
[debug] [2025-08-24T16:59:31.722Z] <<< [apiv2][body] GET https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/connections {}
[debug] [2025-08-24T16:59:31.723Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:31.723Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:31.724Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 [none]
[debug] [2025-08-24T16:59:34.019Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 200
[debug] [2025-08-24T16:59:34.019Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 {"projectNumber":"433967036489","projectId":"wattwhere-f8f65","lifecycleState":"ACTIVE","name":"WattWhere","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-04T10:25:28.285121Z"}
[debug] [2025-08-24T16:59:34.019Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:34.020Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:34.020Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy [none]
[debug] [2025-08-24T16:59:34.375Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy 200
[debug] [2025-08-24T16:59:34.376Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy {"version":1,"etag":"BwY9H1XjARE=","bindings":[{"role":"roles/artifactregistry.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudaicompanion.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.builds.builder","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.developer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/containerregistry.ServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.readTokenAccessor","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/editor","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebase.managementServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.sdkAdminServiceAgent","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.computeRunner","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseauth.admin","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebasehosting.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasemods.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaserules.system","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firestore.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/iam.serviceAccountTokenCreator","members":["serviceAccount:<EMAIL>"]},{"role":"roles/owner","members":["user:<EMAIL>","user:<EMAIL>"]},{"role":"roles/run.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/run.viewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin_withcond_6a995354d163d6fe7b02","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.apiKeysViewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.serviceUsageConsumer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.objectViewer","members":["serviceAccount:<EMAIL>"]}]}
[info] i  To create a new GitHub connection, Secret Manager Admin role (roles/secretmanager.admin) is required on the Developer Connect Service Agent. 
[debug] [2025-08-24T16:59:42.868Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:42.868Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:42.868Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy [none]
[debug] [2025-08-24T16:59:43.885Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy 200
[debug] [2025-08-24T16:59:43.885Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:getIamPolicy {"version":1,"etag":"BwY9H1XjARE=","bindings":[{"role":"roles/artifactregistry.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudaicompanion.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.builds.builder","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.developer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/containerregistry.ServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.readTokenAccessor","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/editor","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebase.managementServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.sdkAdminServiceAgent","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.computeRunner","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseauth.admin","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebasehosting.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasemods.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaserules.system","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firestore.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/iam.serviceAccountTokenCreator","members":["serviceAccount:<EMAIL>"]},{"role":"roles/owner","members":["user:<EMAIL>","user:<EMAIL>"]},{"role":"roles/run.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/run.viewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin_withcond_6a995354d163d6fe7b02","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.apiKeysViewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.serviceUsageConsumer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.objectViewer","members":["serviceAccount:<EMAIL>"]}]}
[debug] [2025-08-24T16:59:43.886Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:43.886Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:43.887Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:setIamPolicy [none]
[debug] [2025-08-24T16:59:43.887Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:setIamPolicy {"policy":{"version":1,"etag":"BwY9H1XjARE=","bindings":[{"role":"roles/artifactregistry.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudaicompanion.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.builds.builder","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.developer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/containerregistry.ServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.readTokenAccessor","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/editor","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebase.managementServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.sdkAdminServiceAgent","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.computeRunner","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseauth.admin","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebasehosting.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasemods.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaserules.system","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firestore.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/iam.serviceAccountTokenCreator","members":["serviceAccount:<EMAIL>"]},{"role":"roles/owner","members":["user:<EMAIL>","user:<EMAIL>"]},{"role":"roles/run.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/run.viewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin_withcond_6a995354d163d6fe7b02","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.apiKeysViewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.serviceUsageConsumer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.objectViewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin","members":["serviceAccount:<EMAIL>"]}]},"updateMask":"bindings"}
[debug] [2025-08-24T16:59:45.643Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:setIamPolicy 200
[debug] [2025-08-24T16:59:45.643Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:setIamPolicy {"version":1,"etag":"BwY9H125s6k=","bindings":[{"role":"roles/artifactregistry.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudaicompanion.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.builds.builder","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.developer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/containerregistry.ServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.readTokenAccessor","members":["serviceAccount:<EMAIL>"]},{"role":"roles/developerconnect.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/editor","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebase.managementServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.sdkAdminServiceAgent","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.computeRunner","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseapphosting.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaseauth.admin","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/firebasehosting.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasemods.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaserules.system","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firestore.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/iam.serviceAccountTokenCreator","members":["serviceAccount:<EMAIL>"]},{"role":"roles/owner","members":["user:<EMAIL>","user:<EMAIL>"]},{"role":"roles/run.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/run.viewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/secretmanager.admin_withcond_6a995354d163d6fe7b02","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.apiKeysViewer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/serviceusage.serviceUsageConsumer","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/storage.objectViewer","members":["serviceAccount:<EMAIL>"]}]}
[info] +  Successfully granted the required role to the Developer Connect Service Agent!
 
[debug] [2025-08-24T16:59:45.646Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:45.646Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:45.647Z] >>> [apiv2][query] POST https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/connections connectionId=apphosting-github-conn-iu1f01p
[debug] [2025-08-24T16:59:45.647Z] >>> [apiv2][body] POST https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/connections {"githubConfig":{"githubApp":"FIREBASE"}}
[debug] [2025-08-24T16:59:47.162Z] <<< [apiv2][status] POST https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/connections 200
[debug] [2025-08-24T16:59:47.162Z] <<< [apiv2][body] POST https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/connections {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-1756054786989-63d1f5dc8dc53-041d562f-c02a20b0","metadata":{"@type":"type.googleapis.com/google.cloud.developerconnect.v1.OperationMetadata","createTime":"2025-08-24T16:59:47.517462363Z","target":"projects/wattwhere-f8f65/locations/us-central1/connections/apphosting-github-conn-iu1f01p","verb":"create","requestedCancellation":false,"apiVersion":"v1"},"done":false}
[debug] [2025-08-24T16:59:47.163Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:47.163Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:59:47.163Z] >>> [apiv2][query] GET https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/operations/operation-1756054786989-63d1f5dc8dc53-041d562f-c02a20b0 [none]
[debug] [2025-08-24T16:59:47.409Z] <<< [apiv2][status] GET https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/operations/operation-1756054786989-63d1f5dc8dc53-041d562f-c02a20b0 200
[debug] [2025-08-24T16:59:47.409Z] <<< [apiv2][body] GET https://developerconnect.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/operations/operation-1756054786989-63d1f5dc8dc53-041d562f-c02a20b0 {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-1756054786989-63d1f5dc8dc53-041d562f-c02a20b0","metadata":{"@type":"type.googleapis.com/google.cloud.developerconnect.v1.OperationMetadata","createTime":"2025-08-24T16:59:47.517462363Z","endTime":"2025-08-24T16:59:47.555158184Z","target":"projects/wattwhere-f8f65/locations/us-central1/connections/apphosting-github-conn-iu1f01p","verb":"create","requestedCancellation":false,"apiVersion":"v1"},"done":true,"response":{"@type":"type.googleapis.com/google.cloud.developerconnect.v1.Connection","name":"projects/wattwhere-f8f65/locations/us-central1/connections/apphosting-github-conn-iu1f01p","createTime":"2025-08-24T16:59:47.511694689Z","updateTime":"2025-08-24T16:59:47.511694689Z","githubConfig":{"githubApp":"FIREBASE"},"installationState":{"stage":"PENDING_USER_OAUTH","message":"Please log in to https://github.com using a robot account and then follow this link to authorize Developer Connect to access that account. After authorization, your GitHub authorization token will be stored in Cloud Secret Manager.","actionUri":"https://accounts.google.com/AccountChooser?continue=https%3A%2F%2Fconsole.cloud.google.com%2Fdeveloper-connect%2Ffinish-setup%3Fstate%3D%257B%2522connection%2522%253A%2B%2522projects%252Fwattwhere-f8f65%252Flocations%252Fus-central1%252Fconnections%252Fapphosting-github-conn-iu1f01p%2522%257D"},"uid":"2693dd09-c74d-4db2-ac67-c218f24d3587"}}
[info] i  Please authorize the Firebase GitHub app by visiting this url: 
[info] i  	http://localhost:9005 
[debug] [2025-08-24T17:00:57.310Z] ExitPromptError: User force closed the prompt with SIGINT
    at Interface.sigint (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\node_modules\@inquirer\core\dist\commonjs\lib\create-prompt.js:101:37)
    at Interface.emit (node:events:518:28)
    at Interface.emit (node:domain:489:12)
    at [_ttyWrite] [as _ttyWrite] (node:internal/readline/interface:1124:18)
    at ReadStream.onkeypress (node:internal/readline/interface:263:20)
    at ReadStream.emit (node:events:530:35)
    at ReadStream.emit (node:domain:489:12)
    at emitKeys (node:internal/readline/utils:370:14)
    at emitKeys.next (<anonymous>)
    at ReadStream.onData (node:internal/readline/emitKeypressEvents:64:36)
[error] 
[error] Error: An unexpected error has occurred.
