const { onRequest } = require("firebase-functions/v2/https");
const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

// Import routes
const authRoutes = require("./routes/auth.routes");
const suggestionRoutes = require("./routes/suggestion.routes");
const userRoutes = require("./routes/user.routes");
const adminRoutes = require("./routes/admin.routes");

// Initialize Express app
const app = express();

// CORS configuration
const corsOptions = {
  origin: [
    'https://wattwhere-f8f65.web.app',
    'https://wattwhere-f8f65.firebaseapp.com',
    'http://localhost:3000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  maxAge: 86400 // Cache preflight for 24 hours
};

// Apply CORS middleware
app.use(cors(corsOptions));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url} - Origin: ${req.headers.origin}`);
  next();
});

// Middleware
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// CORS test endpoint
app.get("/cors-test", (req, res) => {
  console.log("CORS test endpoint hit");
  console.log("Origin:", req.headers.origin);
  console.log("Method:", req.method);
  
  res.status(200).json({
    status: "ok",
    message: "CORS test successful",
    timestamp: new Date().toISOString(),
    origin: req.headers.origin,
    method: req.method,
    headers: req.headers
  });
});

// Simple test route (no auth required)
app.get("/test", (req, res) => {
  res.status(200).json({
    status: "ok",
    message: "Backend is working!",
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url
  });
});

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/suggestions", suggestionRoutes);
app.use("/api/users", userRoutes);
app.use("/api/admin", adminRoutes);

// Health check route
app.get("/api/health", (req, res) => {
  res.status(200).json({
    status: "ok",
    message: "Server is running on Firebase Functions",
    timestamp: new Date().toISOString(),
    cors: "enabled"
  });
});

// Test route without /api prefix
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "ok",
    message: "Direct health check - no /api prefix",
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error("Error:", err.stack);
  res.status(err.statusCode || 500).json({
    status: "error",
    message: err.message || "Internal Server Error",
    error: process.env.NODE_ENV === "development" ? err : {},
  });
});

// Export as Firebase Function v2 with unauthenticated access
exports.api = onRequest({
  cors: true,
  invoker: 'public'
}, app);
