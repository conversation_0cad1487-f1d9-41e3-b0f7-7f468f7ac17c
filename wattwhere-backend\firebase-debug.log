[debug] [2025-08-24T17:51:10.942Z] ----------------------------------------------------------------------
[debug] [2025-08-24T17:51:10.944Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only functions
[debug] [2025-08-24T17:51:10.945Z] CLI Version:   14.13.0
[debug] [2025-08-24T17:51:10.945Z] Platform:      win32
[debug] [2025-08-24T17:51:10.945Z] Node Version:  v22.10.0
[debug] [2025-08-24T17:51:10.945Z] Time:          Sun Aug 24 2025 20:51:10 GMT+0300 (Eastern European Summer Time)
[debug] [2025-08-24T17:51:10.945Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-24T17:51:11.150Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-24T17:51:11.150Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-24T17:51:11.151Z] [iam] checking project wattwhere-f8f65 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-08-24T17:51:11.152Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:11.152Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:11.153Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions [none]
[debug] [2025-08-24T17:51:11.153Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions x-goog-quota-user=projects/wattwhere-f8f65
[debug] [2025-08-24T17:51:11.153Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-08-24T17:51:12.177Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions 200
[debug] [2025-08-24T17:51:12.177Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-08-24T17:51:12.177Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:12.177Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:12.177Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-08-24T17:51:12.177Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-08-24T17:51:13.174Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-08-24T17:51:13.174Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'wattwhere-f8f65'...
[info] 
[info] i  deploying functions 
[debug] [2025-08-24T17:51:13.179Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:13.179Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:13.179Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 [none]
[debug] [2025-08-24T17:51:13.445Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 200
[debug] [2025-08-24T17:51:13.445Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 {"projectNumber":"************","projectId":"wattwhere-f8f65","lifecycleState":"ACTIVE","name":"WattWhere","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-04T10:25:28.285121Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-08-24T17:51:13.447Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:13.447Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:13.447Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/wattwhere-f8f65/adminSdkConfig [none]
[debug] [2025-08-24T17:51:14.436Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/wattwhere-f8f65/adminSdkConfig 200
[debug] [2025-08-24T17:51:14.436Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/wattwhere-f8f65/adminSdkConfig {"projectId":"wattwhere-f8f65","storageBucket":"wattwhere-f8f65.firebasestorage.app"}
[debug] [2025-08-24T17:51:14.437Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:14.437Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:14.437Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/wattwhere-f8f65/configs [none]
[debug] [2025-08-24T17:51:15.083Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/wattwhere-f8f65/configs 200
[debug] [2025-08-24T17:51:15.084Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/wattwhere-f8f65/configs {}
[debug] [2025-08-24T17:51:15.089Z] Validating nodejs source
[debug] [2025-08-24T17:51:16.905Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "cloudinary": "^2.6.1",
    "cors": "^2.8.5",
    "dotenv": "^16.5.0",
    "express": "^5.1.0",
    "firebase-admin": "^12.0.0",
    "firebase-functions": "^6.4.0",
    "multer": "^2.0.0"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-08-24T17:51:16.905Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[debug] [2025-08-24T17:51:16.911Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-08-24T17:51:16.942Z] Found firebase-functions binary at 'D:\WattWhere_Project\wattwhere-fro\wattwhere-backend\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8818

[debug] [2025-08-24T17:51:18.150Z] Got response from /__/functions.yaml {"endpoints":{"api":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"api"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] i  extensions: ensuring required API firebaseextensions.googleapis.com is enabled... 
[debug] [2025-08-24T17:51:22.209Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-24T17:51:22.210Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-24T17:51:22.210Z] [iam] checking project wattwhere-f8f65 for permissions ["firebase.projects.get","firebaseextensions.instances.list"]
[debug] [2025-08-24T17:51:22.210Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:22.210Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:22.211Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions [none]
[debug] [2025-08-24T17:51:22.211Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions x-goog-quota-user=projects/wattwhere-f8f65
[debug] [2025-08-24T17:51:22.211Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-08-24T17:51:23.186Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions 200
[debug] [2025-08-24T17:51:23.187Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-08-24T17:51:23.188Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:23.189Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T17:51:23.189Z] >>> [apiv2][query] GET https://firebaseextensions.googleapis.com/v1beta/projects/wattwhere-f8f65/instances pageSize=100&pageToken=
[debug] [2025-08-24T17:51:24.931Z] <<< [apiv2][status] GET https://firebaseextensions.googleapis.com/v1beta/projects/wattwhere-f8f65/instances 200
[debug] [2025-08-24T17:51:24.932Z] <<< [apiv2][body] GET https://firebaseextensions.googleapis.com/v1beta/projects/wattwhere-f8f65/instances {}
[debug] [2025-08-24T17:51:24.934Z] Failed to validate key PORT: Error: Key PORT is reserved for internal use.
[debug] [2025-08-24T17:51:24.934Z] Failed to validate key FIREBASE_PROJECT_ID: Error: Key FIREBASE_PROJECT_ID starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[debug] [2025-08-24T17:51:24.934Z] Failed to validate key FIREBASE_PRIVATE_KEY_ID: Error: Key FIREBASE_PRIVATE_KEY_ID starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[debug] [2025-08-24T17:51:24.934Z] Failed to validate key FIREBASE_PRIVATE_KEY: Error: Key FIREBASE_PRIVATE_KEY starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[debug] [2025-08-24T17:51:24.934Z] Failed to validate key FIREBASE_CLIENT_EMAIL: Error: Key FIREBASE_CLIENT_EMAIL starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[debug] [2025-08-24T17:51:24.934Z] Failed to validate key FIREBASE_CLIENT_ID: Error: Key FIREBASE_CLIENT_ID starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[debug] [2025-08-24T17:51:24.934Z] Failed to validate key FIREBASE_CLIENT_CERT_URL: Error: Key FIREBASE_CLIENT_CERT_URL starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[error] Error: Failed to load environment variables from .env.:
[error] - Error Key PORT is reserved for internal use.
[error] - Error Key FIREBASE_PROJECT_ID starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[error] - Error Key FIREBASE_PRIVATE_KEY_ID starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[error] - Error Key FIREBASE_PRIVATE_KEY starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[error] - Error Key FIREBASE_CLIENT_EMAIL starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[error] - Error Key FIREBASE_CLIENT_ID starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
[error] - Error Key FIREBASE_CLIENT_CERT_URL starts with a reserved prefix (X_GOOGLE_ FIREBASE_ EXT_)
