[debug] [2025-08-24T16:26:21.834Z] ----------------------------------------------------------------------
[debug] [2025-08-24T16:26:21.837Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only functions
[debug] [2025-08-24T16:26:21.837Z] CLI Version:   14.13.0
[debug] [2025-08-24T16:26:21.837Z] Platform:      win32
[debug] [2025-08-24T16:26:21.837Z] Node Version:  v22.10.0
[debug] [2025-08-24T16:26:21.837Z] Time:          Sun Aug 24 2025 19:26:21 GMT+0300 (Eastern European Summer Time)
[debug] [2025-08-24T16:26:21.838Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-24T16:26:22.008Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-24T16:26:22.008Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-24T16:26:22.008Z] [iam] checking project wattwhere-f8f65 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-08-24T16:26:22.009Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:22.009Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:22.010Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions [none]
[debug] [2025-08-24T16:26:22.010Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions x-goog-quota-user=projects/wattwhere-f8f65
[debug] [2025-08-24T16:26:22.010Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-08-24T16:26:24.096Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions 200
[debug] [2025-08-24T16:26:24.096Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-08-24T16:26:24.097Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:24.097Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:24.097Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-08-24T16:26:24.097Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-08-24T16:26:42.460Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-08-24T16:26:42.461Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/wattwhere-f8f65/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'wattwhere-f8f65'...
[info] 
[info] i  deploying functions 
[debug] [2025-08-24T16:26:42.470Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:42.470Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:42.471Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 [none]
[debug] [2025-08-24T16:26:43.555Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 200
[debug] [2025-08-24T16:26:43.555Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 {"projectNumber":"************","projectId":"wattwhere-f8f65","lifecycleState":"ACTIVE","name":"WattWhere","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-04T10:25:28.285121Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-08-24T16:26:43.568Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:43.568Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:43.568Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/wattwhere-f8f65/adminSdkConfig [none]
[debug] [2025-08-24T16:26:45.172Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/wattwhere-f8f65/adminSdkConfig 200
[debug] [2025-08-24T16:26:45.173Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/wattwhere-f8f65/adminSdkConfig {"projectId":"wattwhere-f8f65","storageBucket":"wattwhere-f8f65.firebasestorage.app"}
[debug] [2025-08-24T16:26:45.174Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:45.174Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:45.174Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/wattwhere-f8f65/configs [none]
[debug] [2025-08-24T16:26:47.042Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/wattwhere-f8f65/configs 200
[debug] [2025-08-24T16:26:47.043Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/wattwhere-f8f65/configs {}
[debug] [2025-08-24T16:26:47.048Z] Validating nodejs source
[debug] [2025-08-24T16:26:48.728Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "cloudinary": "^2.6.1",
    "cors": "^2.8.5",
    "dotenv": "^16.5.0",
    "express": "^5.1.0",
    "firebase-admin": "^12.0.0",
    "firebase-functions": "^6.4.0",
    "multer": "^2.0.0"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true
}
[debug] [2025-08-24T16:26:48.728Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[debug] [2025-08-24T16:26:48.732Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-08-24T16:26:48.772Z] Found firebase-functions binary at 'D:\WattWhere_Project\wattwhere-fro\wattwhere-backend\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8781

[debug] [2025-08-24T16:26:49.961Z] Got response from /__/functions.yaml {"endpoints":{"api":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{"invoker":["public"]},"entryPoint":"api"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] i  extensions: ensuring required API firebaseextensions.googleapis.com is enabled... 
[debug] [2025-08-24T16:26:54.018Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-24T16:26:54.019Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-08-24T16:26:54.019Z] [iam] checking project wattwhere-f8f65 for permissions ["firebase.projects.get","firebaseextensions.instances.list"]
[debug] [2025-08-24T16:26:54.019Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:54.019Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:54.019Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions [none]
[debug] [2025-08-24T16:26:54.019Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions x-goog-quota-user=projects/wattwhere-f8f65
[debug] [2025-08-24T16:26:54.019Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-08-24T16:26:55.080Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions 200
[debug] [2025-08-24T16:26:55.080Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-08-24T16:26:55.081Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:55.081Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:55.082Z] >>> [apiv2][query] GET https://firebaseextensions.googleapis.com/v1beta/projects/wattwhere-f8f65/instances pageSize=100&pageToken=
[debug] [2025-08-24T16:26:56.371Z] <<< [apiv2][status] GET https://firebaseextensions.googleapis.com/v1beta/projects/wattwhere-f8f65/instances 200
[debug] [2025-08-24T16:26:56.371Z] <<< [apiv2][body] GET https://firebaseextensions.googleapis.com/v1beta/projects/wattwhere-f8f65/instances {}
[info] i  functions: preparing functions directory for uploading... 
[info] i  functions: packaged D:\WattWhere_Project\wattwhere-fro\wattwhere-backend\functions (90.11 KB) for uploading 
[debug] [2025-08-24T16:26:56.526Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:56.526Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:56.527Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/projects/wattwhere-f8f65/locations/-/functions [none]
[debug] [2025-08-24T16:26:57.646Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/projects/wattwhere-f8f65/locations/-/functions 200
[debug] [2025-08-24T16:26:57.646Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/projects/wattwhere-f8f65/locations/-/functions {}
[debug] [2025-08-24T16:26:57.647Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:57.647Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:57.648Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/-/functions filter=environment%3D%22GEN_2%22
[debug] [2025-08-24T16:26:58.804Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/-/functions 200
[debug] [2025-08-24T16:26:58.805Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/-/functions {"functions":[{"name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"build":"projects/************/locations/us-central1/builds/3da642e3-50ee-49a9-af5c-a6fca69a65b6","runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-08-24T16:21:46.158557245Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"3caf60b971c11ee18ce938b09efaf5ceace879b0"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","createTime":"2025-08-24T11:51:44.261294764Z","satisfiesPzi":true}]}
[info] i  functions: ensuring required API run.googleapis.com is enabled... 
[info] i  functions: ensuring required API eventarc.googleapis.com is enabled... 
[info] i  functions: ensuring required API pubsub.googleapis.com is enabled... 
[info] i  functions: ensuring required API storage.googleapis.com is enabled... 
[info] i  functions: generating the service identity for pubsub.googleapis.com... 
[debug] [2025-08-24T16:26:58.816Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:58.816Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: generating the service identity for eventarc.googleapis.com... 
[debug] [2025-08-24T16:26:58.817Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:58.817Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:26:58.817Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity [none]
[debug] [2025-08-24T16:26:58.818Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity x-goog-quota-user=projects/************
[debug] [2025-08-24T16:26:58.818Z] >>> [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity {}
[debug] [2025-08-24T16:26:58.821Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity [none]
[debug] [2025-08-24T16:26:58.822Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity x-goog-quota-user=projects/************
[debug] [2025-08-24T16:26:58.822Z] >>> [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity {}
[debug] [2025-08-24T16:27:00.130Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity 200
[debug] [2025-08-24T16:27:00.130Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity {"name":"operations/finished.DONE_OPERATION","done":true,"response":{"@type":"type.googleapis.com/google.api.serviceusage.v1beta1.ServiceIdentity","email":"<EMAIL>","uniqueId":"109231893505853060350"}}
[debug] [2025-08-24T16:27:00.363Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity 200
[debug] [2025-08-24T16:27:00.364Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity {"name":"operations/finished.DONE_OPERATION","done":true,"response":{"@type":"type.googleapis.com/google.api.serviceusage.v1beta1.ServiceIdentity","email":"<EMAIL>","uniqueId":"114865278124515322191"}}
[debug] [2025-08-24T16:27:00.368Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:00.368Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:00.368Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 [none]
[debug] [2025-08-24T16:27:01.429Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 200
[debug] [2025-08-24T16:27:01.430Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/wattwhere-f8f65 {"projectNumber":"************","projectId":"wattwhere-f8f65","lifecycleState":"ACTIVE","name":"WattWhere","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-05-04T10:25:28.285121Z"}
[debug] [2025-08-24T16:27:01.431Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:01.431Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:01.431Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-08-24T16:27:02.846Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-08-24T16:27:02.846Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"containerInfo":"************","serviceTitle":"Compute Engine API","consumer":"projects/************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-08-24T16:27:02.847Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"containerInfo":"************","serviceTitle":"Compute Engine API","consumer":"projects/************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-08-24T16:27:02.848Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:02.848Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:02.849Z] >>> [apiv2][query] GET https://cloudbilling.googleapis.com/v1/projects/wattwhere-f8f65/billingInfo [none]
[debug] [2025-08-24T16:27:06.779Z] <<< [apiv2][status] GET https://cloudbilling.googleapis.com/v1/projects/wattwhere-f8f65/billingInfo 200
[debug] [2025-08-24T16:27:06.779Z] <<< [apiv2][body] GET https://cloudbilling.googleapis.com/v1/projects/wattwhere-f8f65/billingInfo {"name":"projects/wattwhere-f8f65/billingInfo","projectId":"wattwhere-f8f65","billingAccountName":"billingAccounts/018115-212245-1A38FE","billingEnabled":true}
[debug] [2025-08-24T16:27:06.782Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:06.783Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:06.783Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/functions:generateUploadUrl [none]
[debug] [2025-08-24T16:27:08.022Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/functions:generateUploadUrl 200
[debug] [2025-08-24T16:27:08.023Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/functions:generateUploadUrl {"uploadUrl":"https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/6cfb3fe8-c3c3-4ded-a2bf-a5c0e5bdf820.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=FbUFtQw%2Bh5DYmg2hMFVu%2BMdW4VojrFmKzWcWoJYEZJUEPEfEIDJbqotqfbq5kWnniqoc%2FRk0%2FUuvGMAot2Ych6lHOocHG11jHfpyHQRuaSXr5VsaXIxLpqSOvD2KOLzgs3WI%2FnnXD00Jb8%2FgU6%2BjIe3LIMNLteiJ3P%2BBy5wz2t0DZxMy%2FX7Se9DXLCS4ZhkPRufsj6G%2F3GKXL%2B5x%2BpZwoBa%2FmTj3YP7ufE1KwxRg8vsCfwKKFunvPOH8SrbExGDTBRgp3qp2stduwoFW25giQiq9JckukwXD0mGt7ahHvmsakZdvxTnF4ffyBqKwqiRALnpvsFRs%2FYGZto9qjnqjsA%3D%3D","storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"6cfb3fe8-c3c3-4ded-a2bf-a5c0e5bdf820.zip"}}
[debug] [2025-08-24T16:27:08.024Z] >>> [apiv2][query] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/6cfb3fe8-c3c3-4ded-a2bf-a5c0e5bdf820.zip GoogleAccessId=service-************%40gcf-admin-robot.iam.gserviceaccount.com&Expires=**********&Signature=FbUFtQw%2Bh5DYmg2hMFVu%2BMdW4VojrFmKzWcWoJYEZJUEPEfEIDJbqotqfbq5kWnniqoc%2FRk0%2FUuvGMAot2Ych6lHOocHG11jHfpyHQRuaSXr5VsaXIxLpqSOvD2KOLzgs3WI%2FnnXD00Jb8%2FgU6%2BjIe3LIMNLteiJ3P%2BBy5wz2t0DZxMy%2FX7Se9DXLCS4ZhkPRufsj6G%2F3GKXL%2B5x%2BpZwoBa%2FmTj3YP7ufE1KwxRg8vsCfwKKFunvPOH8SrbExGDTBRgp3qp2stduwoFW25giQiq9JckukwXD0mGt7ahHvmsakZdvxTnF4ffyBqKwqiRALnpvsFRs%2FYGZto9qjnqjsA%3D%3D
[debug] [2025-08-24T16:27:08.024Z] >>> [apiv2][body] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/6cfb3fe8-c3c3-4ded-a2bf-a5c0e5bdf820.zip [stream]
[debug] [2025-08-24T16:27:09.675Z] <<< [apiv2][status] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/6cfb3fe8-c3c3-4ded-a2bf-a5c0e5bdf820.zip 200
[debug] [2025-08-24T16:27:09.676Z] <<< [apiv2][body] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/6cfb3fe8-c3c3-4ded-a2bf-a5c0e5bdf820.zip [omitted]
[info] +  functions: functions folder uploaded successfully 
[info] i  functions: updating Node.js 22 (2nd Gen) function api(us-central1)... 
[debug] [2025-08-24T16:27:09.683Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:09.683Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:09.684Z] >>> [apiv2][query] PATCH https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/functions/api updateMask=name%2CbuildConfig.runtime%2CbuildConfig.entryPoint%2CbuildConfig.source.storageSource.bucket%2CbuildConfig.source.storageSource.object%2CbuildConfig.environmentVariables%2CbuildConfig.sourceToken%2CserviceConfig.environmentVariables%2CserviceConfig.ingressSettings%2CserviceConfig.timeoutSeconds%2CserviceConfig.serviceAccountEmail%2CserviceConfig.availableMemory%2CserviceConfig.minInstanceCount%2CserviceConfig.maxInstanceCount%2CserviceConfig.maxInstanceRequestConcurrency%2CserviceConfig.availableCpu%2CserviceConfig.vpcConnector%2CserviceConfig.vpcConnectorEgressSettings%2Clabels
[debug] [2025-08-24T16:27:09.684Z] >>> [apiv2][body] PATCH https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/functions/api {"name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"6cfb3fe8-c3c3-4ded-a2bf-a5c0e5bdf820.zip"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""}},"serviceConfig":{"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":null,"timeoutSeconds":null,"serviceAccountEmail":null,"availableMemory":"256Mi","minInstanceCount":null,"maxInstanceCount":null,"maxInstanceRequestConcurrency":80,"availableCpu":"1","vpcConnector":null,"vpcConnectorEgressSettings":null},"labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"}}
[debug] [2025-08-24T16:27:12.078Z] <<< [apiv2][status] PATCH https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/functions/api 200
[debug] [2025-08-24T16:27:12.078Z] <<< [apiv2][body] PATCH https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/functions/api {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2"},"done":false}
[debug] [2025-08-24T16:27:12.079Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:12.079Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:12.080Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:27:13.000Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:27:13.001Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","state":"NOT_STARTED"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"UPDATE_FUNCTION"},"done":false}
[debug] [2025-08-24T16:27:13.511Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:27:13.512Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:13.512Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:13.512Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:27:14.630Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:27:14.630Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:27:15.634Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:27:15.634Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:15.634Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:15.634Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:27:18.993Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:27:18.993Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:27:21.005Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:27:21.006Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:21.006Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:21.007Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:27:21.337Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:27:21.337Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:27:25.339Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:27:25.339Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:25.339Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:25.339Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:27:26.238Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:27:26.238Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:27:34.252Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:27:34.253Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:34.253Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:34.253Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:27:35.449Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:27:35.450Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:27:45.451Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:27:45.452Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:45.452Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:27:45.452Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:28:02.653Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:28:02.653Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","message":"Updating Cloud Run service","state":"IN_PROGRESS","resource":"projects/wattwhere-f8f65/locations/us-central1/services/api","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/api?project=wattwhere-f8f65","stateMessages":[{"severity":"INFO","type":"CloudRunServiceNewRevisionTrafficInfo","message":"A new revision will be deployed serving with 100% traffic."}]}],"sourceToken":"Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE=","operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:28:02.654Z] Got source token Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE= for region us-central1
[debug] [2025-08-24T16:28:12.656Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:28:12.657Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:28:12.657Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:28:12.657Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:28:34.779Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:28:34.779Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","message":"Updating Cloud Run service","state":"IN_PROGRESS","resource":"projects/wattwhere-f8f65/locations/us-central1/services/api","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/api?project=wattwhere-f8f65","stateMessages":[{"severity":"INFO","type":"CloudRunServiceNewRevisionTrafficInfo","message":"A new revision will be deployed serving with 100% traffic."}]}],"sourceToken":"Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE=","operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:28:34.779Z] Got source token Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE= for region us-central1
[debug] [2025-08-24T16:28:44.783Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:28:44.784Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:28:44.784Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:28:44.784Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:29:03.402Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:29:03.402Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","message":"Updating Cloud Run service","state":"IN_PROGRESS","resource":"projects/wattwhere-f8f65/locations/us-central1/services/api","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/api?project=wattwhere-f8f65","stateMessages":[{"severity":"INFO","type":"CloudRunServiceNewRevisionTrafficInfo","message":"A new revision will be deployed serving with 100% traffic."}]}],"sourceToken":"Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE=","operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:29:03.402Z] Got source token Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE= for region us-central1
[debug] [2025-08-24T16:29:13.418Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:29:13.419Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:29:13.419Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:29:13.419Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:29:17.468Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:29:17.468Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","message":"Updating Cloud Run service","state":"IN_PROGRESS","resource":"projects/wattwhere-f8f65/locations/us-central1/services/api","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/api?project=wattwhere-f8f65","stateMessages":[{"severity":"INFO","type":"CloudRunServiceNewRevisionTrafficInfo","message":"A new revision will be deployed serving with 100% traffic."}]}],"sourceToken":"Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE=","operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:29:17.468Z] Got source token Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE= for region us-central1
[debug] [2025-08-24T16:29:27.481Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:29:27.481Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:29:27.481Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:29:27.481Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:29:31.796Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:29:31.797Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","message":"Updating Cloud Run service","state":"IN_PROGRESS","resource":"projects/wattwhere-f8f65/locations/us-central1/services/api","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/api?project=wattwhere-f8f65","stateMessages":[{"severity":"INFO","type":"CloudRunServiceNewRevisionTrafficInfo","message":"A new revision will be deployed serving with 100% traffic."}]}],"sourceToken":"Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE=","operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":false}
[debug] [2025-08-24T16:29:31.797Z] Got source token Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE= for region us-central1
[debug] [2025-08-24T16:29:41.811Z] [update-default-us-central1-api] Retrying task index 0
[debug] [2025-08-24T16:29:41.812Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:29:41.812Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:29:41.812Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd [none]
[debug] [2025-08-24T16:30:02.523Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd 200
[debug] [2025-08-24T16:30:02.524Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd {"name":"projects/wattwhere-f8f65/locations/us-central1/operations/operation-*************-63d1ee93100db-80f974c2-c19ededd","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-08-24T16:27:12.402020441Z","endTime":"2025-08-24T16:29:36.066049930Z","target":"projects/wattwhere-f8f65/locations/us-central1/functions/api","verb":"update","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00010-wow","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","labels":{"deployment-tool":"cloudfunctions","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","satisfiesPzi":true},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/fcc97d2d-b0f6-4bd6-b754-7c1622121f35?project=************"},{"name":"SERVICE","message":"Updating Cloud Run service","state":"COMPLETE","resource":"projects/wattwhere-f8f65/locations/us-central1/services/api","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/api?project=wattwhere-f8f65","stateMessages":[{"severity":"INFO","type":"CloudRunServiceNewRevisionTrafficInfo","message":"A new revision will be deployed serving with 100% traffic."}]}],"sourceToken":"Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE=","operationType":"UPDATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35"},"done":true,"response":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/wattwhere-f8f65/locations/us-central1/functions/api","buildConfig":{"build":"projects/************/locations/us-central1/builds/fcc97d2d-b0f6-4bd6-b754-7c1622121f35","runtime":"nodejs22","entryPoint":"api","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"api/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/wattwhere-f8f65/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/wattwhere-f8f65/locations/us-central1/services/api","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}","GCLOUD_PROJECT":"wattwhere-f8f65","EVENTARC_CLOUD_EVENT_SOURCE":"projects/wattwhere-f8f65/locations/us-central1/services/api","FUNCTION_TARGET":"api","LOG_EXECUTION_ID":"true"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://api-hckcwa25ra-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"api-00011-bey","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-08-24T16:27:12.404106801Z","labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8"},"environment":"GEN_2","url":"https://us-central1-wattwhere-f8f65.cloudfunctions.net/api","createTime":"2025-08-24T11:51:44.261294764Z","satisfiesPzi":true}}
[debug] [2025-08-24T16:30:02.524Z] Got source token Cldwcm9qZWN0cy80MzM5NjcwMzY0ODkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy9mY2M5N2QyZC1iMGY2LTRiZDYtYjc1NC03YzE2MjIxMjFmMzUSZnVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3dhdHR3aGVyZS1mOGY2NS9nY2YtYXJ0aWZhY3RzL3dhdHR3aGVyZS0tZjhmNjVfX3VzLS1jZW50cmFsMV9fYXBpOnZlcnNpb25fMRjJ8M3T0AwiPHByb2plY3RzL3dhdHR3aGVyZS1mOGY2NS9sb2NhdGlvbnMvdXMtY2VudHJhbDEvZnVuY3Rpb25zL2FwaSoLCI37rMUGEPj85AQyCG5vZGVqczIyOnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMjI6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczIyQAE= for region us-central1
[debug] [2025-08-24T16:30:02.526Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:30:02.526Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:30:02.526Z] >>> [apiv2][query] GET https://run.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/services/api:getIamPolicy [none]
[debug] [2025-08-24T16:30:52.959Z] *** [apiv2] error from fetch(https://run.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/services/api:getIamPolicy, {"headers":{},"method":"GET"}): FetchError: request to https://run.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/services/api:getIamPolicy failed, reason: socket hang up
[error] Failed to get the IAM Policy on the Service projects/wattwhere-f8f65/locations/us-central1/services/api
[debug] [2025-08-24T16:30:52.975Z] Total Function Deployment time: 223282
[debug] [2025-08-24T16:30:52.975Z] 1 Functions Deployed
[debug] [2025-08-24T16:30:52.975Z] 1 Functions Errored
[debug] [2025-08-24T16:30:52.975Z] 0 Function Deployments Aborted
[debug] [2025-08-24T16:30:52.975Z] Average Function Deployment time: 223281
[info] 
[info] Functions deploy had errors with the following functions:
	api(us-central1)
[info] 
[info] Unable to set the invoker for the IAM policy on the following functions:
	api(us-central1)
[info] 
[info] Some common causes of this:
[info] 
[info] - You may not have the roles/functions.admin IAM role. Note that roles/functions.developer does not allow you to change IAM policies.
[info] 
[info] - An organization policy that restricts Network Access on your project.
[info] Function URL (api(us-central1)): https://api-hckcwa25ra-uc.a.run.app
[debug] [2025-08-24T16:30:53.613Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:30:53.614Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-08-24T16:30:53.615Z] >>> [apiv2][query] GET https://artifactregistry.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts [none]
[debug] [2025-08-24T16:31:15.902Z] <<< [apiv2][status] GET https://artifactregistry.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts 200
[debug] [2025-08-24T16:31:15.903Z] <<< [apiv2][body] GET https://artifactregistry.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts {"name":"projects/wattwhere-f8f65/locations/us-central1/repositories/gcf-artifacts","format":"DOCKER","description":"This repository is created and used by Cloud Functions for storing function docker images.","labels":{"goog-managed-by":"cloudfunctions"},"createTime":"2025-08-24T11:51:55.779794Z","updateTime":"2025-08-24T16:27:40.689002Z","mode":"STANDARD_REPOSITORY","cleanupPolicies":{"firebase-functions-cleanup":{"id":"firebase-functions-cleanup","action":"DELETE","condition":{"tagState":"ANY","olderThan":"86400s"}}},"sizeBytes":"232924187","vulnerabilityScanningConfig":{"lastEnableTime":"2025-08-24T11:51:44.447675886Z","enablementState":"SCANNING_DISABLED","enablementStateReason":"API containerscanning.googleapis.com is not enabled."},"satisfiesPzi":true,"registryUri":"us-central1-docker.pkg.dev/wattwhere-f8f65/gcf-artifacts"}
[debug] [2025-08-24T16:31:15.904Z] Functions deploy failed.
[debug] [2025-08-24T16:31:15.904Z] {
  "endpoint": {
    "id": "api",
    "project": "wattwhere-f8f65",
    "region": "us-central1",
    "entryPoint": "api",
    "platform": "gcfv2",
    "runtime": "nodejs22",
    "httpsTrigger": {
      "invoker": [
        "public"
      ]
    },
    "labels": {
      "deployment-tool": "cli-firebase"
    },
    "serviceAccount": null,
    "ingressSettings": null,
    "availableMemoryMb": null,
    "timeoutSeconds": null,
    "maxInstances": null,
    "minInstances": null,
    "concurrency": 80,
    "vpc": null,
    "environmentVariables": {
      "FIREBASE_CONFIG": "{\"projectId\":\"wattwhere-f8f65\",\"storageBucket\":\"wattwhere-f8f65.firebasestorage.app\"}",
      "GCLOUD_PROJECT": "wattwhere-f8f65",
      "EVENTARC_CLOUD_EVENT_SOURCE": "projects/wattwhere-f8f65/locations/us-central1/services/api",
      "FUNCTION_TARGET": "api",
      "LOG_EXECUTION_ID": "true"
    },
    "codebase": "default",
    "runServiceId": "api",
    "cpu": 1,
    "securityLevel": "SECURE_ALWAYS",
    "targetedByOnly": false,
    "hash": "dd3eb26c23f8720e6ba7a8ed7852cab82ae150a8",
    "uri": "https://api-hckcwa25ra-uc.a.run.app"
  },
  "op": "set invoker",
  "original": {
    "name": "FirebaseError",
    "children": [],
    "exit": 1,
    "message": "Failed to get the IAM Policy on the Service projects/wattwhere-f8f65/locations/us-central1/services/api",
    "original": {
      "name": "FirebaseError",
      "children": [],
      "exit": 1,
      "message": "Failed to make request to https://run.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/services/api:getIamPolicy",
      "original": {
        "message": "request to https://run.googleapis.com/v1/projects/wattwhere-f8f65/locations/us-central1/services/api:getIamPolicy failed, reason: socket hang up",
        "type": "system",
        "errno": "ECONNRESET",
        "code": "ECONNRESET"
      },
      "status": 500
    },
    "status": 500,
    "code": 500
  }
}
[debug] [2025-08-24T16:31:16.181Z] Error: Failed to set invoker function api in region us-central1
    at C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\deploy\functions\release\fabricator.js:52:11
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Fabricator.updateV2Function (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\deploy\functions\release\fabricator.js:438:13)
    at async Fabricator.updateEndpoint (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\deploy\functions\release\fabricator.js:160:13)
    at async handle (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\deploy\functions\release\fabricator.js:89:17)
[error] 
[error] Error: There was an error deploying functions
