[{"D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\index.js": "1", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\reportWebVitals.js": "2", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\App.js": "3", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\pages\\Home.jsx": "4", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\Confirmation.jsx": "5", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\Login.jsx": "6", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\SuggestionForm.jsx": "7", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\AdminDashboard.jsx": "8", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\UserProfile.jsx": "9", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\utils\\mapStyles.js": "10", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\contexts\\AuthContext.js": "11", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\config\\firebase.js": "12", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\services\\api.service.js": "13", "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\ProtectedRoute.jsx": "14"}, {"size": 535, "mtime": 1748107191448, "results": "15", "hashOfConfig": "16"}, {"size": 362, "mtime": 1747813672877, "results": "17", "hashOfConfig": "16"}, {"size": 3274, "mtime": 1748116046102, "results": "18", "hashOfConfig": "16"}, {"size": 27105, "mtime": 1748010859139, "results": "19", "hashOfConfig": "16"}, {"size": 6420, "mtime": 1747936217236, "results": "20", "hashOfConfig": "16"}, {"size": 7461, "mtime": 1747924759605, "results": "21", "hashOfConfig": "16"}, {"size": 22678, "mtime": 1748116046101, "results": "22", "hashOfConfig": "16"}, {"size": 134102, "mtime": 1748126643732, "results": "23", "hashOfConfig": "16"}, {"size": 12658, "mtime": 1748124034505, "results": "24", "hashOfConfig": "16"}, {"size": 3438, "mtime": 1747851847804, "results": "25", "hashOfConfig": "16"}, {"size": 3589, "mtime": 1747921893427, "results": "26", "hashOfConfig": "16"}, {"size": 782, "mtime": 1748125503102, "results": "27", "hashOfConfig": "16"}, {"size": 3259, "mtime": 1756052549227, "results": "28", "hashOfConfig": "16"}, {"size": 1487, "mtime": 1747853740600, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ws13tc", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 34, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\index.js", [], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\reportWebVitals.js", [], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\App.js", [], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\pages\\Home.jsx", [], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\Confirmation.jsx", ["72"], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\Login.jsx", ["73"], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\SuggestionForm.jsx", ["74", "75", "76", "77", "78"], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\AdminDashboard.jsx", ["79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112"], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\UserProfile.jsx", ["113"], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\utils\\mapStyles.js", [], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\contexts\\AuthContext.js", [], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\config\\firebase.js", [], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\services\\api.service.js", [], [], "D:\\WattWhere_Project\\wattwhere-fro\\wattwhere-frontend\\src\\components\\ProtectedRoute.jsx", [], [], {"ruleId": "114", "severity": 1, "message": "115", "line": 23, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 23, "endColumn": 17}, {"ruleId": "114", "severity": 1, "message": "115", "line": 23, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 23, "endColumn": 17}, {"ruleId": "114", "severity": 1, "message": "118", "line": 1, "column": 40, "nodeType": "116", "messageId": "117", "endLine": 1, "endColumn": 49}, {"ruleId": "114", "severity": 1, "message": "119", "line": 10, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 10, "endColumn": 9}, {"ruleId": "114", "severity": 1, "message": "120", "line": 24, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 24, "endColumn": 13}, {"ruleId": "114", "severity": 1, "message": "115", "line": 60, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 60, "endColumn": 17}, {"ruleId": "114", "severity": 1, "message": "121", "line": 62, "column": 11, "nodeType": "116", "messageId": "117", "endLine": 62, "endColumn": 22}, {"ruleId": "114", "severity": 1, "message": "122", "line": 1, "column": 38, "nodeType": "116", "messageId": "117", "endLine": 1, "endColumn": 49}, {"ruleId": "114", "severity": 1, "message": "123", "line": 31, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 31, "endColumn": 17}, {"ruleId": "114", "severity": 1, "message": "124", "line": 32, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 32, "endColumn": 7}, {"ruleId": "114", "severity": 1, "message": "125", "line": 33, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 33, "endColumn": 14}, {"ruleId": "114", "severity": 1, "message": "126", "line": 34, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 34, "endColumn": 8}, {"ruleId": "114", "severity": 1, "message": "127", "line": 51, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 51, "endColumn": 14}, {"ruleId": "114", "severity": 1, "message": "128", "line": 52, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 52, "endColumn": 8}, {"ruleId": "114", "severity": 1, "message": "129", "line": 53, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 53, "endColumn": 9}, {"ruleId": "114", "severity": 1, "message": "130", "line": 61, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 61, "endColumn": 13}, {"ruleId": "114", "severity": 1, "message": "131", "line": 66, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 66, "endColumn": 11}, {"ruleId": "114", "severity": 1, "message": "132", "line": 70, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 70, "endColumn": 22}, {"ruleId": "114", "severity": 1, "message": "133", "line": 75, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 75, "endColumn": 8}, {"ruleId": "114", "severity": 1, "message": "134", "line": 76, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 76, "endColumn": 7}, {"ruleId": "114", "severity": 1, "message": "135", "line": 77, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 77, "endColumn": 7}, {"ruleId": "114", "severity": 1, "message": "136", "line": 80, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 80, "endColumn": 16}, {"ruleId": "114", "severity": 1, "message": "137", "line": 81, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 81, "endColumn": 7}, {"ruleId": "114", "severity": 1, "message": "138", "line": 82, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 82, "endColumn": 7}, {"ruleId": "114", "severity": 1, "message": "139", "line": 83, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 83, "endColumn": 9}, {"ruleId": "114", "severity": 1, "message": "140", "line": 102, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 102, "endColumn": 12}, {"ruleId": "114", "severity": 1, "message": "141", "line": 103, "column": 3, "nodeType": "116", "messageId": "117", "endLine": 103, "endColumn": 7}, {"ruleId": "114", "severity": 1, "message": "142", "line": 112, "column": 7, "nodeType": "116", "messageId": "117", "endLine": 112, "endColumn": 24}, {"ruleId": "114", "severity": 1, "message": "143", "line": 124, "column": 7, "nodeType": "116", "messageId": "117", "endLine": 124, "endColumn": 16}, {"ruleId": "114", "severity": 1, "message": "144", "line": 138, "column": 10, "nodeType": "116", "messageId": "117", "endLine": 138, "endColumn": 20}, {"ruleId": "114", "severity": 1, "message": "145", "line": 139, "column": 10, "nodeType": "116", "messageId": "117", "endLine": 139, "endColumn": 19}, {"ruleId": "114", "severity": 1, "message": "146", "line": 140, "column": 10, "nodeType": "116", "messageId": "117", "endLine": 140, "endColumn": 15}, {"ruleId": "114", "severity": 1, "message": "147", "line": 238, "column": 10, "nodeType": "116", "messageId": "117", "endLine": 238, "endColumn": 17}, {"ruleId": "114", "severity": 1, "message": "148", "line": 238, "column": 19, "nodeType": "116", "messageId": "117", "endLine": 238, "endColumn": 29}, {"ruleId": "114", "severity": 1, "message": "149", "line": 239, "column": 10, "nodeType": "116", "messageId": "117", "endLine": 239, "endColumn": 19}, {"ruleId": "114", "severity": 1, "message": "150", "line": 239, "column": 21, "nodeType": "116", "messageId": "117", "endLine": 239, "endColumn": 33}, {"ruleId": "151", "severity": 1, "message": "152", "line": 359, "column": 6, "nodeType": "153", "endLine": 359, "endColumn": 19, "suggestions": "154"}, {"ruleId": "151", "severity": 1, "message": "155", "line": 371, "column": 6, "nodeType": "153", "endLine": 371, "endColumn": 36, "suggestions": "156"}, {"ruleId": "114", "severity": 1, "message": "157", "line": 436, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 436, "endColumn": 27}, {"ruleId": "114", "severity": 1, "message": "158", "line": 505, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 505, "endColumn": 21}, {"ruleId": "114", "severity": 1, "message": "159", "line": 580, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 580, "endColumn": 25}, {"ruleId": "114", "severity": 1, "message": "115", "line": 34, "column": 9, "nodeType": "116", "messageId": "117", "endLine": 34, "endColumn": 17}, "no-unused-vars", "'isMobile' is assigned a value but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "'Avatar' is defined but never used.", "'InputLabel' is defined but never used.", "'currentUser' is assigned a value but never used.", "'useCallback' is defined but never used.", "'InputAdornment' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Stack' is defined but never used.", "'Description' is defined but never used.", "'Image' is defined but never used.", "'Search' is defined but never used.", "'FilterList' is defined but never used.", "'MoreVert' is defined but never used.", "'BatteryChargingFull' is defined but never used.", "'Share' is defined but never used.", "'Info' is defined but never used.", "'Help' is defined but never used.", "'ArrowDownward' is defined but never used.", "'Sort' is defined but never used.", "'Edit' is defined but never used.", "'Delete' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'mapContainerStyle' is assigned a value but never used.", "'libraries' is assigned a value but never used.", "'searchTerm' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'mapZoom' is assigned a value but never used.", "'setMapZoom' is assigned a value but never used.", "'mapCenter' is assigned a value but never used.", "'setMapCenter' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDemandAnalytics'. Either include it or remove the dependency array.", "ArrayExpression", ["160"], "React Hook useEffect has missing dependencies: 'loadDemandAnalytics' and 'submissions.length'. Either include them or remove the dependency array.", ["161"], "'calculateAnalytics' is assigned a value but never used.", "'handleSearch' is assigned a value but never used.", "'renderStatusChip' is assigned a value but never used.", {"desc": "162", "fix": "163"}, {"desc": "164", "fix": "165"}, "Update the dependencies array to be: [currentUser, loadDemandAnalytics]", {"range": "166", "text": "167"}, "Update the dependencies array to be: [demandRadius, loadDemandAnalytics, minSubmissions, submissions.length]", {"range": "168", "text": "169"}, [10041, 10054], "[currentU<PERSON>, loadDemandAnalytics]", [10380, 10410], "[demandRadius, loadDemandAnalytics, minSubmissions, submissions.length]"]