import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Paper,
  TextField,
  Stack,
  Avatar,
  IconButton,
  CircularProgress,
  useTheme,
  useMediaQuery,
  InputAdornment,
  Tooltip,
  Alert,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
  Select,
  MenuItem,
  InputLabel,
  Divider,
  Snackbar
} from '@mui/material';
import {
  LocationOn,
  MyLocation,
  ArrowBack,
  CloudUpload,
  Delete,
  ZoomIn,
  ZoomOut
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { GoogleMap, useJsApiLoader, Marker } from '@react-google-maps/api';
import { modernMapStyle, markerIcons } from '../utils/mapStyles';
import { useAuth } from '../contexts/AuthContext';
import { suggestionsAPI } from '../services/api.service';

// Google Maps container style
const mapContainerStyle = {
  width: '100%',
  height: '350px',
  borderRadius: '12px'
};

// Default center (will be overridden by user's location)
const defaultCenter = {
  lat: 40.7128, // New York
  lng: -74.0060
};

const libraries = ["places"];

const SuggestionForm = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  const [address, setAddress] = useState('');
  const [description, setDescription] = useState('');
  const [photo, setPhoto] = useState(null);
  const [photoPreview, setPhotoPreview] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [mapCenter, setMapCenter] = useState(defaultCenter);
  const [markerPosition, setMarkerPosition] = useState(null);
  const [mapZoom, setMapZoom] = useState(15);
  const [mapError, setMapError] = useState('');
  const [hasEV, setHasEV] = useState('');
  const [weeklyUse, setWeeklyUse] = useState('');
  const [error, setError] = useState('');
  const [showError, setShowError] = useState(false);

  // Load Google Maps API
  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey: process.env.REACT_APP_GOOGLE_MAPS_API_KEY,
    libraries
  });

  const handleBack = () => {
    navigate('/');
  };

  // Handle map click to set marker
  const handleMapClick = useCallback((event) => {
    const lat = event.latLng.lat();
    const lng = event.latLng.lng();

    setMarkerPosition({ lat, lng });
    setAddress(`Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}`);

    // Optional: Use Google's Geocoding API to get the address from coordinates
    // This would require additional API calls
  }, []);

  // Zoom controls
  const handleZoomIn = () => {
    setMapZoom(prev => Math.min(prev + 1, 20));
  };

  const handleZoomOut = () => {
    setMapZoom(prev => Math.max(prev - 1, 1));
  };

  const handleGetCurrentLocation = () => {
    setIsGettingLocation(true);
    setMapError('');

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;

          // Update map center and marker
          setMapCenter({ lat, lng });
          setMarkerPosition({ lat, lng });

          // For demo purposes, just show the coordinates
          setAddress(`Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}`);
          setIsGettingLocation(false);
        },
        (error) => {
          console.error('Error getting location:', error);
          setIsGettingLocation(false);
          setMapError('Unable to retrieve your location. Please enter the address manually or click on the map.');
        }
      );
    } else {
      setIsGettingLocation(false);
      setMapError('Geolocation is not supported by your browser. Please enter the address manually or click on the map.');
    }
  };

  const handlePhotoChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setPhoto(selectedFile);

      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setPhotoPreview(event.target.result);
      };
      reader.readAsDataURL(selectedFile);
    }
  };

  const handleRemovePhoto = () => {
    setPhoto(null);
    setPhotoPreview('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!address.trim() || !markerPosition) {
      setMapError('Please select a location on the map or use the current location button.');
      return;
    }

    if (!hasEV) {
      setError('Please indicate whether you own an electric vehicle.');
      setShowError(true);
      return;
    }

    if (!weeklyUse) {
      setError('Please indicate how many times you would use this charging point weekly.');
      setShowError(true);
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Create form data for submission
      const formData = new FormData();
      formData.append('address', address.trim());
      formData.append('coordinates', JSON.stringify(markerPosition));
      formData.append('description', description.trim());
      formData.append('hasEV', hasEV === 'yes' ? 'true' : 'false');
      formData.append('weeklyUse', weeklyUse.toString());

      // Add photo if available
      if (photo) {
        formData.append('photo', photo);
      }

      // Send data to backend
      const response = await suggestionsAPI.createSuggestion(formData);

      if (response.status === 'success') {
        // Navigate to confirmation page
        navigate('/confirmation');
      } else {
        throw new Error(response.message || 'Failed to submit suggestion');
      }
    } catch (error) {
      console.error('Submission error:', error);
      setError(error.message || 'An error occurred while submitting your suggestion. Please try again.');
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle error snackbar close
  const handleErrorClose = () => {
    setShowError(false);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: '#F5F5F5',
        pt: { xs: 8, md: 12 },
        pb: 6
      }}
    >
      {/* Error Snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleErrorClose}
        message={error}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      />
      <Container maxWidth="md">
        <IconButton
          onClick={handleBack}
          sx={{
            mb: 2,
            color: '#222222',
            '&:hover': { bgcolor: 'rgba(46, 196, 182, 0.08)' }
          }}
        >
          <ArrowBack />
        </IconButton>

        <Paper
          elevation={0}
          sx={{
            p: { xs: 3, md: 5 },
            borderRadius: 4,
            bgcolor: 'white',
            boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Decorative Elements */}
          <Box
            sx={{
              position: 'absolute',
              width: '200px',
              height: '200px',
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(46,196,182,0.08) 0%, rgba(46,196,182,0) 70%)',
              top: '-100px',
              right: '-100px',
              zIndex: 0
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              width: '200px',
              height: '200px',
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(255,90,95,0.08) 0%, rgba(255,90,95,0) 70%)',
              bottom: '-100px',
              left: '-100px',
              zIndex: 0
            }}
          />

          <Box sx={{ position: 'relative', zIndex: 1 }}>
            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 4 }}>
              <LocationOn sx={{ color: '#FF5A5F', fontSize: 28 }} />
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 800,
                  color: '#222222'
                }}
              >
                Suggest a Charging Spot
              </Typography>
            </Stack>

            <form onSubmit={handleSubmit}>
              <Stack spacing={3}>
                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      mb: 1,
                      color: '#222222'
                    }}
                  >
                    Location
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Enter address or use current location"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    variant="outlined"
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LocationOn sx={{ color: '#2EC4B6' }} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <Tooltip title="Use current location">
                            <IconButton
                              onClick={handleGetCurrentLocation}
                              disabled={isGettingLocation}
                              sx={{ color: '#FF5A5F' }}
                            >
                              {isGettingLocation ? (
                                <CircularProgress size={24} color="inherit" />
                              ) : (
                                <MyLocation />
                              )}
                            </IconButton>
                          </Tooltip>
                        </InputAdornment>
                      ),
                      sx: {
                        borderRadius: 2,
                        bgcolor: '#F5F5F5',
                        '&:hover': {
                          bgcolor: '#F0F0F0'
                        }
                      }
                    }}
                  />

                  {mapError && (
                    <Alert
                      severity="warning"
                      sx={{ mt: 2, borderRadius: 2 }}
                    >
                      {mapError}
                    </Alert>
                  )}

                  {/* Google Map */}
                  <Box sx={{ mt: 2, position: 'relative' }}>
                    {isLoaded ? (
                      <>
                        <Paper
                          elevation={0}
                          sx={{
                            borderRadius: 3,
                            overflow: 'hidden',
                            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                            border: '1px solid rgba(0,0,0,0.05)',
                          }}
                        >
                          <GoogleMap
                            mapContainerStyle={mapContainerStyle}
                            center={mapCenter}
                            zoom={mapZoom}
                            onClick={handleMapClick}
                            options={{
                              fullscreenControl: false,
                              streetViewControl: false,
                              mapTypeControl: false,
                              zoomControl: false,
                              styles: modernMapStyle,
                              disableDefaultUI: true,
                              gestureHandling: 'greedy',
                            }}
                          >
                            {markerPosition && (
                              <Marker
                                position={markerPosition}
                                animation={2} // DROP animation
                                icon={markerIcons.selected}
                              />
                            )}
                          </GoogleMap>
                        </Paper>

                        {/* Custom zoom controls */}
                        <Box
                          sx={{
                            position: 'absolute',
                            right: 16,
                            top: 16,
                            display: 'flex',
                            flexDirection: 'column',
                            bgcolor: 'white',
                            borderRadius: 2,
                            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                            p: 0.5,
                            '& .MuiIconButton-root': {
                              color: '#222222',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                bgcolor: 'rgba(46, 196, 182, 0.1)',
                                color: '#2EC4B6',
                              }
                            }
                          }}
                        >
                          <IconButton onClick={handleZoomIn} size="small">
                            <ZoomIn />
                          </IconButton>
                          <Divider sx={{ my: 0.5 }} />
                          <IconButton onClick={handleZoomOut} size="small">
                            <ZoomOut />
                          </IconButton>
                        </Box>
                      </>
                    ) : (
                      <Paper
                        elevation={0}
                        sx={{
                          height: 350,
                          bgcolor: '#F5F5F5',
                          borderRadius: 3,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                          border: '1px solid rgba(0,0,0,0.05)',
                        }}
                      >
                        {loadError ? (
                          <Typography color="error">
                            Error loading maps. Please try again later.
                          </Typography>
                        ) : (
                          <Box sx={{ textAlign: 'center' }}>
                            <CircularProgress color="primary" size={40} sx={{ mb: 2 }} />
                            <Typography variant="body2" color="text.secondary">
                              Loading map...
                            </Typography>
                          </Box>
                        )}
                      </Paper>
                    )}
                  </Box>

                  <Typography
                    variant="caption"
                    sx={{
                      display: 'block',
                      mt: 1,
                      color: 'text.secondary',
                      textAlign: 'center'
                    }}
                  >
                    Click on the map to set a location or use the "Current Location" button
                  </Typography>
                </Box>

                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      mb: 1,
                      color: '#222222'
                    }}
                  >
                    Description (Optional)
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Why is this a good location for a charging station?"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    variant="outlined"
                    multiline
                    rows={3}
                    InputProps={{
                      sx: {
                        borderRadius: 2,
                        bgcolor: '#F5F5F5',
                        '&:hover': {
                          bgcolor: '#F0F0F0'
                        }
                      }
                    }}
                  />
                </Box>

                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      mb: 1,
                      color: '#222222'
                    }}
                  >
                    Do you own an electric vehicle?
                  </Typography>
                  <FormControl required sx={{ mb: 3 }}>
                    <RadioGroup
                      row
                      value={hasEV}
                      onChange={(e) => setHasEV(e.target.value)}
                    >
                      <FormControlLabel
                        value="yes"
                        control={<Radio sx={{ color: '#2EC4B6', '&.Mui-checked': { color: '#2EC4B6' } }} />}
                        label="Yes"
                      />
                      <FormControlLabel
                        value="no"
                        control={<Radio sx={{ color: '#2EC4B6', '&.Mui-checked': { color: '#2EC4B6' } }} />}
                        label="No"
                      />
                    </RadioGroup>
                  </FormControl>
                </Box>

                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      mb: 1,
                      color: '#222222'
                    }}
                  >
                    If there was a charging point here, how many times would you use it weekly?
                  </Typography>
                  <FormControl fullWidth required sx={{ mb: 3 }}>
                    <Select
                      value={weeklyUse}
                      onChange={(e) => setWeeklyUse(e.target.value)}
                      displayEmpty
                      sx={{
                        borderRadius: 2,
                        bgcolor: '#F5F5F5',
                        '&:hover': {
                          bgcolor: '#F0F0F0'
                        }
                      }}
                    >
                      <MenuItem value="" disabled>Select frequency</MenuItem>
                      <MenuItem value="1">1 time per week</MenuItem>
                      <MenuItem value="2">2 times per week</MenuItem>
                      <MenuItem value="3">3 times per week</MenuItem>
                      <MenuItem value="4">4 times per week</MenuItem>
                      <MenuItem value="5">5 or more times per week</MenuItem>
                    </Select>
                  </FormControl>
                </Box>

                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      mb: 1,
                      color: '#222222'
                    }}
                  >
                    Photo (Optional)
                  </Typography>

                  {!photoPreview ? (
                    <Button
                      component="label"
                      variant="outlined"
                      startIcon={<CloudUpload />}
                      sx={{
                        py: 1.5,
                        px: 2,
                        borderColor: 'rgba(0,0,0,0.12)',
                        borderStyle: 'dashed',
                        color: '#666666',
                        borderRadius: 2,
                        '&:hover': {
                          borderColor: '#2EC4B6',
                          bgcolor: 'rgba(46, 196, 182, 0.04)'
                        }
                      }}
                    >
                      Upload Photo
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={handlePhotoChange}
                      />
                    </Button>
                  ) : (
                    <Box sx={{ position: 'relative', width: 'fit-content' }}>
                      <Box
                        component="img"
                        src={photoPreview}
                        alt="Location preview"
                        sx={{
                          width: '100%',
                          maxWidth: 300,
                          height: 'auto',
                          borderRadius: 2,
                          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                        }}
                      />
                      <IconButton
                        onClick={handleRemovePhoto}
                        sx={{
                          position: 'absolute',
                          top: 8,
                          right: 8,
                          bgcolor: 'rgba(255,255,255,0.8)',
                          color: '#FF5A5F',
                          '&:hover': {
                            bgcolor: 'rgba(255,255,255,0.9)',
                          }
                        }}
                      >
                        <Delete />
                      </IconButton>
                    </Box>
                  )}
                </Box>

                <Button
                  type="submit"
                  variant="contained"
                  size="large"
                  disabled={isLoading || !address.trim()}
                  sx={{
                    bgcolor: '#2EC4B6',
                    color: 'white',
                    py: 1.5,
                    fontWeight: 700,
                    fontSize: '1.1rem',
                    borderRadius: 99,
                    mt: 2,
                    '&:hover': {
                      bgcolor: '#23C3B1',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 6px 20px rgba(46, 196, 182, 0.3)',
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  {isLoading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    'Submit Suggestion'
                  )}
                </Button>
              </Stack>
            </form>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default SuggestionForm;
