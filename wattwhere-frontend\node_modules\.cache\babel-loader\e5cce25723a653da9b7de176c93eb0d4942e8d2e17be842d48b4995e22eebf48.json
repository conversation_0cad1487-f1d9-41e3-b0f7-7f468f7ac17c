{"ast": null, "code": "import _objectSpread from\"D:/WattWhere_Project/wattwhere-fro/wattwhere-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{auth}from'../config/firebase';const API_URL=process.env.NODE_ENV==='production'?'https://api-hckcwa25ra-uc.a.run.app/api':'http://localhost:5000/api';/**\n * Get the current user's ID token\n * @returns {Promise<string>} The ID token\n */const getToken=async()=>{const user=auth.currentUser;if(!user){throw new Error('User not authenticated');}return await user.getIdToken();};/**\n * Make an API request with authentication\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Fetch options\n * @returns {Promise<Object>} Response data\n */const apiRequest=async function(endpoint){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};try{// Get authentication token\nconst token=await getToken();// Set default headers\nconst headers=_objectSpread({'Content-Type':'application/json','Authorization':\"Bearer \".concat(token)},options.headers);// Make the request\nconst response=await fetch(\"\".concat(API_URL).concat(endpoint),_objectSpread(_objectSpread({},options),{},{headers}));// Parse the response\nconst data=await response.json();// Check for errors\nif(!response.ok){throw new Error(data.message||'Something went wrong');}return data;}catch(error){console.error('API request error:',error);throw error;}};/**\n * Authentication API\n */const authAPI={verifyToken:async token=>{const response=await fetch(\"\".concat(API_URL,\"/auth/verify-token\"),{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({token})});return await response.json();}};/**\n * Suggestions API\n */const suggestionsAPI={createSuggestion:async formData=>{try{const token=await getToken();console.log('Sending suggestion request to:',\"\".concat(API_URL,\"/suggestions\"));console.log('FormData contents:');for(let[key,value]of formData.entries()){console.log(key,value);}const response=await fetch(\"\".concat(API_URL,\"/suggestions\"),{method:'POST',headers:{'Authorization':\"Bearer \".concat(token)},body:formData// FormData for file upload\n});console.log('Response status:',response.status);console.log('Response headers:',response.headers);if(!response.ok){const errorText=await response.text();console.error('Error response:',errorText);throw new Error(\"HTTP \".concat(response.status,\": \").concat(errorText));}const result=await response.json();console.log('Success response:',result);return result;}catch(error){console.error('createSuggestion error:',error);throw error;}},getUserSuggestions:async()=>{return await apiRequest('/suggestions/user/me');},getSuggestion:async id=>{return await apiRequest(\"/suggestions/\".concat(id));}};/**\n * User API\n */const userAPI={getCurrentUser:async()=>{return await apiRequest('/users/me');},updateProfile:async userData=>{return await apiRequest('/users/me',{method:'PATCH',body:JSON.stringify(userData)});}};/**\n * Admin API\n */const adminAPI={getAllSuggestions:async function(){let filters=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};const queryParams=new URLSearchParams(filters).toString();return await apiRequest(\"/admin/suggestions?\".concat(queryParams));},updateSuggestionStatus:async(id,status)=>{return await apiRequest(\"/admin/suggestions/\".concat(id,\"/status\"),{method:'PATCH',body:JSON.stringify({status})});},getAnalytics:async()=>{return await apiRequest('/admin/analytics');},getDemandAnalytics:async function(){let options=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};const queryParams=new URLSearchParams(options).toString();return await apiRequest(\"/admin/demand-analytics?\".concat(queryParams));}};export{authAPI,suggestionsAPI,userAPI,adminAPI};", "map": {"version": 3, "names": ["auth", "API_URL", "process", "env", "NODE_ENV", "getToken", "user", "currentUser", "Error", "getIdToken", "apiRequest", "endpoint", "options", "arguments", "length", "undefined", "token", "headers", "_objectSpread", "concat", "response", "fetch", "data", "json", "ok", "message", "error", "console", "authAPI", "verifyToken", "method", "body", "JSON", "stringify", "suggestionsAPI", "createSuggestion", "formData", "log", "key", "value", "entries", "status", "errorText", "text", "result", "getUserSuggestions", "getSuggestion", "id", "userAPI", "getCurrentUser", "updateProfile", "userData", "adminAPI", "getAllSuggestions", "filters", "queryParams", "URLSearchParams", "toString", "updateSuggestionStatus", "getAnalytics", "getDemandAnalytics"], "sources": ["D:/WattWhere_Project/wattwhere-fro/wattwhere-frontend/src/services/api.service.js"], "sourcesContent": ["import { auth } from '../config/firebase';\n\nconst API_URL = process.env.NODE_ENV === 'production'\n  ? 'https://api-hckcwa25ra-uc.a.run.app/api'\n  : 'http://localhost:5000/api';\n\n/**\n * Get the current user's ID token\n * @returns {Promise<string>} The ID token\n */\nconst getToken = async () => {\n  const user = auth.currentUser;\n  if (!user) {\n    throw new Error('User not authenticated');\n  }\n  return await user.getIdToken();\n};\n\n/**\n * Make an API request with authentication\n * @param {string} endpoint - API endpoint\n * @param {Object} options - Fetch options\n * @returns {Promise<Object>} Response data\n */\nconst apiRequest = async (endpoint, options = {}) => {\n  try {\n    // Get authentication token\n    const token = await getToken();\n\n    // Set default headers\n    const headers = {\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`,\n      ...options.headers\n    };\n\n    // Make the request\n    const response = await fetch(`${API_URL}${endpoint}`, {\n      ...options,\n      headers\n    });\n\n    // Parse the response\n    const data = await response.json();\n\n    // Check for errors\n    if (!response.ok) {\n      throw new Error(data.message || 'Something went wrong');\n    }\n\n    return data;\n  } catch (error) {\n    console.error('API request error:', error);\n    throw error;\n  }\n};\n\n/**\n * Authentication API\n */\nconst authAPI = {\n  verifyToken: async (token) => {\n    const response = await fetch(`${API_URL}/auth/verify-token`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({ token })\n    });\n\n    return await response.json();\n  }\n};\n\n/**\n * Suggestions API\n */\nconst suggestionsAPI = {\n  createSuggestion: async (formData) => {\n    try {\n      const token = await getToken();\n\n      console.log('Sending suggestion request to:', `${API_URL}/suggestions`);\n      console.log('FormData contents:');\n      for (let [key, value] of formData.entries()) {\n        console.log(key, value);\n      }\n\n      const response = await fetch(`${API_URL}/suggestions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        },\n        body: formData // FormData for file upload\n      });\n\n      console.log('Response status:', response.status);\n      console.log('Response headers:', response.headers);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Error response:', errorText);\n        throw new Error(`HTTP ${response.status}: ${errorText}`);\n      }\n\n      const result = await response.json();\n      console.log('Success response:', result);\n      return result;\n    } catch (error) {\n      console.error('createSuggestion error:', error);\n      throw error;\n    }\n  },\n\n  getUserSuggestions: async () => {\n    return await apiRequest('/suggestions/user/me');\n  },\n\n  getSuggestion: async (id) => {\n    return await apiRequest(`/suggestions/${id}`);\n  }\n};\n\n/**\n * User API\n */\nconst userAPI = {\n  getCurrentUser: async () => {\n    return await apiRequest('/users/me');\n  },\n\n  updateProfile: async (userData) => {\n    return await apiRequest('/users/me', {\n      method: 'PATCH',\n      body: JSON.stringify(userData)\n    });\n  }\n};\n\n/**\n * Admin API\n */\nconst adminAPI = {\n  getAllSuggestions: async (filters = {}) => {\n    const queryParams = new URLSearchParams(filters).toString();\n    return await apiRequest(`/admin/suggestions?${queryParams}`);\n  },\n\n  updateSuggestionStatus: async (id, status) => {\n    return await apiRequest(`/admin/suggestions/${id}/status`, {\n      method: 'PATCH',\n      body: JSON.stringify({ status })\n    });\n  },\n\n  getAnalytics: async () => {\n    return await apiRequest('/admin/analytics');\n  },\n\n  getDemandAnalytics: async (options = {}) => {\n    const queryParams = new URLSearchParams(options).toString();\n    return await apiRequest(`/admin/demand-analytics?${queryParams}`);\n  }\n};\n\nexport {\n  authAPI,\n  suggestionsAPI,\n  userAPI,\n  adminAPI\n};\n"], "mappings": "0IAAA,OAASA,IAAI,KAAQ,oBAAoB,CAEzC,KAAM,CAAAC,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,YAAY,CACjD,yCAAyC,CACzC,2BAA2B,CAE/B;AACA;AACA;AACA,GACA,KAAM,CAAAC,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,KAAM,CAAAC,IAAI,CAAGN,IAAI,CAACO,WAAW,CAC7B,GAAI,CAACD,IAAI,CAAE,CACT,KAAM,IAAI,CAAAE,KAAK,CAAC,wBAAwB,CAAC,CAC3C,CACA,MAAO,MAAM,CAAAF,IAAI,CAACG,UAAU,CAAC,CAAC,CAChC,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,KAAM,CAAAC,UAAU,CAAG,cAAAA,CAAOC,QAAQ,CAAmB,IAAjB,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC9C,GAAI,CACF;AACA,KAAM,CAAAG,KAAK,CAAG,KAAM,CAAAX,QAAQ,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAY,OAAO,CAAAC,aAAA,EACX,cAAc,CAAE,kBAAkB,CAClC,eAAe,WAAAC,MAAA,CAAYH,KAAK,CAAE,EAC/BJ,OAAO,CAACK,OAAO,CACnB,CAED;AACA,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAF,MAAA,CAAIlB,OAAO,EAAAkB,MAAA,CAAGR,QAAQ,EAAAO,aAAA,CAAAA,aAAA,IAC7CN,OAAO,MACVK,OAAO,EACR,CAAC,CAEF;AACA,KAAM,CAAAK,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAElC;AACA,GAAI,CAACH,QAAQ,CAACI,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAhB,KAAK,CAACc,IAAI,CAACG,OAAO,EAAI,sBAAsB,CAAC,CACzD,CAEA,MAAO,CAAAH,IAAI,CACb,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAE,OAAO,CAAG,CACdC,WAAW,CAAE,KAAO,CAAAb,KAAK,EAAK,CAC5B,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAF,MAAA,CAAIlB,OAAO,uBAAsB,CAC3D6B,MAAM,CAAE,MAAM,CACdb,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDc,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEjB,KAAM,CAAC,CAChC,CAAC,CAAC,CAEF,MAAO,MAAM,CAAAI,QAAQ,CAACG,IAAI,CAAC,CAAC,CAC9B,CACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAW,cAAc,CAAG,CACrBC,gBAAgB,CAAE,KAAO,CAAAC,QAAQ,EAAK,CACpC,GAAI,CACF,KAAM,CAAApB,KAAK,CAAG,KAAM,CAAAX,QAAQ,CAAC,CAAC,CAE9BsB,OAAO,CAACU,GAAG,CAAC,gCAAgC,IAAAlB,MAAA,CAAKlB,OAAO,gBAAc,CAAC,CACvE0B,OAAO,CAACU,GAAG,CAAC,oBAAoB,CAAC,CACjC,IAAK,GAAI,CAACC,GAAG,CAAEC,KAAK,CAAC,EAAI,CAAAH,QAAQ,CAACI,OAAO,CAAC,CAAC,CAAE,CAC3Cb,OAAO,CAACU,GAAG,CAACC,GAAG,CAAEC,KAAK,CAAC,CACzB,CAEA,KAAM,CAAAnB,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAF,MAAA,CAAIlB,OAAO,iBAAgB,CACrD6B,MAAM,CAAE,MAAM,CACdb,OAAO,CAAE,CACP,eAAe,WAAAE,MAAA,CAAYH,KAAK,CAClC,CAAC,CACDe,IAAI,CAAEK,QAAS;AACjB,CAAC,CAAC,CAEFT,OAAO,CAACU,GAAG,CAAC,kBAAkB,CAAEjB,QAAQ,CAACqB,MAAM,CAAC,CAChDd,OAAO,CAACU,GAAG,CAAC,mBAAmB,CAAEjB,QAAQ,CAACH,OAAO,CAAC,CAElD,GAAI,CAACG,QAAQ,CAACI,EAAE,CAAE,CAChB,KAAM,CAAAkB,SAAS,CAAG,KAAM,CAAAtB,QAAQ,CAACuB,IAAI,CAAC,CAAC,CACvChB,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEgB,SAAS,CAAC,CAC3C,KAAM,IAAI,CAAAlC,KAAK,SAAAW,MAAA,CAASC,QAAQ,CAACqB,MAAM,OAAAtB,MAAA,CAAKuB,SAAS,CAAE,CAAC,CAC1D,CAEA,KAAM,CAAAE,MAAM,CAAG,KAAM,CAAAxB,QAAQ,CAACG,IAAI,CAAC,CAAC,CACpCI,OAAO,CAACU,GAAG,CAAC,mBAAmB,CAAEO,MAAM,CAAC,CACxC,MAAO,CAAAA,MAAM,CACf,CAAE,MAAOlB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAEDmB,kBAAkB,CAAE,KAAAA,CAAA,GAAY,CAC9B,MAAO,MAAM,CAAAnC,UAAU,CAAC,sBAAsB,CAAC,CACjD,CAAC,CAEDoC,aAAa,CAAE,KAAO,CAAAC,EAAE,EAAK,CAC3B,MAAO,MAAM,CAAArC,UAAU,iBAAAS,MAAA,CAAiB4B,EAAE,CAAE,CAAC,CAC/C,CACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,OAAO,CAAG,CACdC,cAAc,CAAE,KAAAA,CAAA,GAAY,CAC1B,MAAO,MAAM,CAAAvC,UAAU,CAAC,WAAW,CAAC,CACtC,CAAC,CAEDwC,aAAa,CAAE,KAAO,CAAAC,QAAQ,EAAK,CACjC,MAAO,MAAM,CAAAzC,UAAU,CAAC,WAAW,CAAE,CACnCoB,MAAM,CAAE,OAAO,CACfC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACkB,QAAQ,CAC/B,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,QAAQ,CAAG,CACfC,iBAAiB,CAAE,cAAAA,CAAA,CAAwB,IAAjB,CAAAC,OAAO,CAAAzC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACpC,KAAM,CAAA0C,WAAW,CAAG,GAAI,CAAAC,eAAe,CAACF,OAAO,CAAC,CAACG,QAAQ,CAAC,CAAC,CAC3D,MAAO,MAAM,CAAA/C,UAAU,uBAAAS,MAAA,CAAuBoC,WAAW,CAAE,CAAC,CAC9D,CAAC,CAEDG,sBAAsB,CAAE,KAAAA,CAAOX,EAAE,CAAEN,MAAM,GAAK,CAC5C,MAAO,MAAM,CAAA/B,UAAU,uBAAAS,MAAA,CAAuB4B,EAAE,YAAW,CACzDjB,MAAM,CAAE,OAAO,CACfC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEQ,MAAO,CAAC,CACjC,CAAC,CAAC,CACJ,CAAC,CAEDkB,YAAY,CAAE,KAAAA,CAAA,GAAY,CACxB,MAAO,MAAM,CAAAjD,UAAU,CAAC,kBAAkB,CAAC,CAC7C,CAAC,CAEDkD,kBAAkB,CAAE,cAAAA,CAAA,CAAwB,IAAjB,CAAAhD,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACrC,KAAM,CAAA0C,WAAW,CAAG,GAAI,CAAAC,eAAe,CAAC5C,OAAO,CAAC,CAAC6C,QAAQ,CAAC,CAAC,CAC3D,MAAO,MAAM,CAAA/C,UAAU,4BAAAS,MAAA,CAA4BoC,WAAW,CAAE,CAAC,CACnE,CACF,CAAC,CAED,OACE3B,OAAO,CACPM,cAAc,CACdc,OAAO,CACPI,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}